import numpy as np
import matplotlib.pyplot as plt
from AdjustAngle import AdjustAngle

#导入初始方向与正北的夹角
delta_psi = 0

#导入重力加速度
g = 9.8

def Infante_3d_with_current(t,x,u,current_vector, flex_force=None):
    """
    Infante AUV动力学模型，包含海流影响和柔性动力学力
    
    参数:
    t - 时间
    x - 状态向量 [u, v, w, q, r, xp, yp, zp, theta, psi]
    u - 控制输入 [Fu, deltav, deltah]
    current_vector - 海流向量 [uc, vc, wc]
    flex_force - 柔性力 [Fx, Fy, Fz]，默认为None
    
    返回:
    dx - 状态导数
    """
    # 控制输入
    Fu = u[0]      # 推力
    deltav = u[1]  # 垂直舵角
    deltah = u[2]  # 水平舵角
    
    # 海流速度
    uc = current_vector[0]
    vc = current_vector[1]
    wc = current_vector[2]
    
    # 柔性力，如果未提供则为零
    if flex_force is None:
        Fx_flex, Fy_flex, Fz_flex = 0.0, 0.0, 0.0
    else:
        Fx_flex, Fy_flex, Fz_flex = flex_force
    
    # 物理参数
    # 质量矩阵元素
    m1 = 500
    m2 = 800
    m3 = 800
    I1 = 50
    I2 = 60
    
    # 阻尼系数
    d1 = 80
    d2 = 160
    d3 = 160
    d4 = 50
    d5 = 50
    
    # 重力项
    g1 = 0
    
    # 控制矩阵
    b1 = 0
    b2 = 200
    b3 = 200
    
    # 状态变量
    u1 = x[0]  # 纵向速度
    v1 = x[1]  # 横向速度
    w1 = x[2]  # 垂向速度
    q1 = x[3]  # 纵摇角速率
    r1 = x[4]  # 横摇角速率
    xp = x[5]  # x位置
    yp = x[6]  # y位置
    zp = x[7]  # z位置
    theta = x[8]  # 俯仰角
    psi = x[9]  # 航向角
    
    # 系统动力学
    du = (m2*v1*r1-m3*w1*q1-d1*u1+Fu + Fx_flex)/m1 # 添加柔性力
    dv = (-m1*u1*r1-d2*v1 + Fy_flex)/m2 # 添加柔性力
    dw = (m1*u1*q1-d3*w1+g1 + Fz_flex)/m3 # 添加柔性力
    dq = (-b2*deltah-d4*q1)/I1
    dr = (b3*deltav-d5*r1)/I2
    
    # 位置与姿态的运动学方程
    dxp = u1*np.cos(psi) - v1*np.sin(psi) + uc
    dyp = u1*np.sin(psi) + v1*np.cos(psi) + vc
    dzp = w1 + wc
    dtheta = q1
    dpsi = r1
    
    # 返回导数向量
    dx = np.array([du, dv, dw, dq, dr, dxp, dyp, dzp, dtheta, dpsi])
    return dx
