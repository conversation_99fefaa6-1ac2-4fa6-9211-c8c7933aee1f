#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUV编队仿真演示版本
展示：
1. 图注位置在左上角
2. 适度混乱的初始状态，然后排成一字型
3. 向目标区域(60,80)前进
4. 在目标区域设置11个点与AUV一一对应
5. 先到达的AUV停留在目标点
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from matplotlib.patches import Circle
import time

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

class AUVFormationDemo:
    def __init__(self):
        self.num_auvs = 10
        self.dt = 0.1  # 时间步长
        self.max_steps = 2000
        
        # 目标区域设置
        self.target_area = np.array([60.0, 80.0])
        self.target_radius = 5.0
        
        # 在目标区域设置11个点（10个AUV + 1个中心点）
        self.target_positions = np.array([
            [60.0, 80.0],   # 中心点
            [58.0, 82.0],   # AUV1
            [62.0, 82.0],   # AUV2
            [56.0, 78.0],   # AUV3
            [64.0, 78.0],   # AUV4
            [58.0, 78.0],   # AUV5
            [62.0, 78.0],   # AUV6
            [60.0, 76.0],   # AUV7
            [60.0, 84.0],   # AUV8
            [56.0, 82.0],   # AUV9
            [64.0, 82.0],   # AUV10
        ])
        
        # 初始化AUV状态
        self.init_auvs()
        
        # 仿真阶段
        self.phase = 1  # 1: 混乱到一字型, 2: 一字型到圆形, 3: 圆形到目标, 4: 目标分配
        self.auv_reached = [False] * self.num_auvs
        
    def init_auvs(self):
        """初始化AUV位置 - 在混乱区域(0-30km, 0-30km)内随机分布"""
        np.random.seed(42)

        # 混乱区域设置：横坐标0-30km，纵坐标0-30km
        chaos_area_min_x = 0.0
        chaos_area_max_x = 30.0
        chaos_area_min_y = 0.0
        chaos_area_max_y = 30.0

        self.positions = []
        self.velocities = []
        self.targets = []

        for i in range(self.num_auvs):
            # 在混乱区域内随机生成位置
            x_pos = np.random.uniform(chaos_area_min_x, chaos_area_max_x)
            y_pos = np.random.uniform(chaos_area_min_y, chaos_area_max_y)

            pos = np.array([x_pos, y_pos])
            vel = np.array([0.0, 0.0])

            # 初始目标是圆形编队位置
            center = np.array([15.0, 15.0])  # 混乱区域中心
            radius = 8.0
            angle = 2 * np.pi * i / self.num_auvs
            target = center + radius * np.array([np.cos(angle), np.sin(angle)])

            self.positions.append(pos)
            self.velocities.append(vel)
            self.targets.append(target)

        self.positions = np.array(self.positions)
        self.velocities = np.array(self.velocities)
        self.targets = np.array(self.targets)
        
    def update_targets(self):
        """根据当前阶段更新目标位置"""
        if self.phase == 1:
            # 阶段1: 混乱区域到圆形编队
            # 检查是否形成圆形编队
            circle_formed = True
            for i in range(self.num_auvs):
                dist_to_target = np.linalg.norm(self.positions[i] - self.targets[i])
                if dist_to_target > 1.0:
                    circle_formed = False
                    break

            if circle_formed:
                self.phase = 2
                print("阶段1完成: 形成圆形编队")
                # 设置向目标区域移动，保持圆形编队
                center_offset = self.target_area - np.array([15.0, 15.0])
                for i in range(self.num_auvs):
                    self.targets[i] += center_offset
                    
        elif self.phase == 2:
            # 阶段2: 圆形编队向目标区域移动
            formation_center = np.mean(self.positions, axis=0)
            dist_to_target_area = np.linalg.norm(formation_center - self.target_area)

            if dist_to_target_area < 8.0:
                self.phase = 3
                print("阶段2完成: 到达目标区域")
                # 设置最终目标位置（一一对应）
                for i in range(self.num_auvs):
                    self.targets[i] = self.target_positions[i+1]  # 跳过中心点

        elif self.phase == 3:
            # 阶段3: 一对一位置分配
            # 检查每个AUV是否到达目标
            for i in range(self.num_auvs):
                if not self.auv_reached[i]:
                    dist_to_final = np.linalg.norm(self.positions[i] - self.targets[i])
                    if dist_to_final < 0.5:
                        self.auv_reached[i] = True
                        print(f"AUV{i+1} 到达目标位置!")
                        
    def update_physics(self):
        """更新AUV物理状态"""
        max_speed = 2.0
        max_accel = 1.0
        
        for i in range(self.num_auvs):
            if self.phase == 3 and self.auv_reached[i]:
                # 已到达目标的AUV停留不动
                self.velocities[i] = np.array([0.0, 0.0])
                continue
                
            # 计算朝向目标的力
            to_target = self.targets[i] - self.positions[i]
            dist_to_target = np.linalg.norm(to_target)
            
            if dist_to_target > 0.1:
                desired_vel = (to_target / dist_to_target) * max_speed
                
                # 简单的速度控制
                accel = desired_vel - self.velocities[i]
                accel_mag = np.linalg.norm(accel)
                if accel_mag > max_accel:
                    accel = (accel / accel_mag) * max_accel
                    
                self.velocities[i] += accel * self.dt
                
                # 限制最大速度
                vel_mag = np.linalg.norm(self.velocities[i])
                if vel_mag > max_speed:
                    self.velocities[i] = (self.velocities[i] / vel_mag) * max_speed
                    
            # 更新位置
            self.positions[i] += self.velocities[i] * self.dt
            
    def run_simulation(self):
        """运行仿真"""
        plt.ion()
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # 存储轨迹
        trajectories = [[] for _ in range(self.num_auvs)]
        
        for step in range(self.max_steps):
            # 更新目标
            self.update_targets()
            
            # 更新物理状态
            self.update_physics()
            
            # 记录轨迹
            for i in range(self.num_auvs):
                trajectories[i].append(self.positions[i].copy())
            
            # 绘图（每10步更新一次）
            if step % 10 == 0:
                ax.clear()
                
                # 设置坐标范围
                ax.set_xlim(-5, 100)
                ax.set_ylim(-5, 100)
                ax.set_aspect('equal')
                
                # 绘制海流背景（简化）
                x_bg = np.linspace(0, 95, 20)
                y_bg = np.linspace(0, 95, 20)
                X_bg, Y_bg = np.meshgrid(x_bg, y_bg)
                U_bg = np.ones_like(X_bg) * 0.5
                V_bg = np.ones_like(Y_bg) * 0.3
                ax.quiver(X_bg, Y_bg, U_bg, V_bg, alpha=0.3, scale=20, color='blue')
                
                # 绘制目标区域
                target_circle = Circle(self.target_area, self.target_radius, 
                                     fill=False, color='green', linestyle='--', linewidth=2)
                ax.add_patch(target_circle)
                ax.plot(self.target_area[0], self.target_area[1], 'g*', markersize=15)
                
                # 绘制目标位置点
                for i, pos in enumerate(self.target_positions):
                    ax.plot(pos[0], pos[1], 'ro', markersize=8, alpha=0.7)
                    ax.text(pos[0]+1, pos[1]+1, f'T{i}', fontsize=8)
                
                # 绘制AUV轨迹
                colors = plt.cm.rainbow(np.linspace(0, 1, self.num_auvs))
                legend_elements = []

                for i in range(self.num_auvs):
                    if len(trajectories[i]) > 1:
                        traj = np.array(trajectories[i])
                        line, = ax.plot(traj[:, 0], traj[:, 1], '-', color=colors[i],
                                       linewidth=1.5, alpha=0.7)
                        legend_elements.append((line, f'AUV{i+1}'))

                    # 绘制当前位置
                    marker = 's' if self.auv_reached[i] else 'o'
                    ax.plot(self.positions[i, 0], self.positions[i, 1],
                           marker, color=colors[i], markersize=8)

                # 添加特殊图例元素
                from matplotlib.lines import Line2D
                legend_elements.extend([
                    (Line2D([0], [0], color='green', linestyle='--', linewidth=2), '目标区域'),
                    (Line2D([0], [0], marker='*', color='green', linestyle='None', markersize=10), '目标中心'),
                    (Line2D([0], [0], marker='o', color='red', linestyle='None', markersize=8), '目标点'),
                    (Line2D([0], [0], marker='o', color='black', linestyle='None', markersize=8), '运行中'),
                    (Line2D([0], [0], marker='s', color='black', linestyle='None', markersize=8), '已到达')
                ])
                
                # 设置标题和图例
                phase_names = ['混乱→圆形', '圆形→目标', '目标分配']
                ax.set_title(f'AUV编队仿真 - 阶段{self.phase}: {phase_names[self.phase-1]} (步数: {step})',
                           fontsize=14)
                ax.set_xlabel('X [km]', fontsize=12)
                ax.set_ylabel('Y [km]', fontsize=12)
                
                # 图注放在左上角
                if legend_elements:
                    lines, labels = zip(*legend_elements)
                    ax.legend(lines, labels, loc='upper left', fontsize=8, framealpha=0.7)
                
                plt.pause(0.01)
                
            # 检查是否所有AUV都到达目标
            if self.phase == 3 and all(self.auv_reached):
                print("所有AUV都已到达目标位置！仿真完成。")
                break
                
        plt.ioff()
        plt.savefig('auv_formation_demo.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return trajectories

if __name__ == "__main__":
    demo = AUVFormationDemo()
    trajectories = demo.run_simulation()
    print("仿真演示完成！")
