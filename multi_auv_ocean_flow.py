import numpy as np
import matplotlib
# 使用默认后端，允许图形窗口弹出
import matplotlib.pyplot as plt
from AdjustAngle import AdjustAngle
import xarray as xr
from ocean_field import OceanCurrentField
from infante import Infante_3d_with_current
from mpl_toolkits.mplot3d import Axes3D
from limitmaxmin import LimitMaxMin
from scipy.interpolate import RegularGridInterpolator
from matplotlib.cm import get_cmap
from pyproj import Geod
from scipy.ndimage import zoom
import time
import os
import datetime
import matplotlib.patches as patches
import pandas as pd
import networkx as nx  # 用于处理网络拓扑结构
import matplotlib.animation as animation  # 添加动画支持
from matplotlib.colors import Normalize  # 用于颜色归一化
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

# --- 加载海流数据 ---
ds = xr.open_dataset("D:/ocean_data/cmems_mod_glo_phy_my_0.083deg_P1D-m_1751179968305.nc")
uo = ds['uo'].isel(time=0).values  # 形状应为 (depth, lat, lon)
print("提取后 uo shape:", uo.shape)

print(ds['uo'].dims)

vo = ds['vo'].isel(time=0).values
lat = ds['latitude'].values
lon = ds['longitude'].values
depth = ds['depth'].values
print(f"海图纬度范围: {lat.min()} ~ {lat.max()}")
print(f"海图经度范围: {lon.min()} ~ {lon.max()}")
print(f"海图深度范围: {depth.min()} ~ {depth.max()}")

# 构建插值器：注意维度顺序 (depth, lat, lon)
uo_corrected = uo
vo_corrected = vo
uo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),  # 深度倒序使其从浅到深
    uo_corrected[::-1, :, :]  # 数据也同步倒序
)
vo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),
    vo_corrected[::-1, :, :]
)

print("uo shape:", uo.shape)  # 应该是 (13, x, x)
# 取一个固定位置，检查不同深度上的流速变化
lat_idx = 6  # 中间纬度
lon_idx = 6  # 中间经度

print("\n=== 检查不同深度的流速变化 ===")
for i, d in enumerate(depth):
    print(f"Depth = {d:.1f}m: uo = {uo[i, lat_idx, lon_idx]:.4f}, vo = {vo[i, lat_idx, lon_idx]:.4f}")

print("depth:", depth.shape)  # 应该是 (13,)
print("lat:", lat.shape)
print("lon:", lon.shape)

# 初始化插值器
ocean = OceanCurrentField(uo_corrected, vo_corrected, lat, lon, depth)

# 将初始位置移到图像中心
lat0 = (lat[0] + lat[-1]) / 2  # 使用纬度的中点
lon0 = (lon[0] + lon[-1]) / 2  # 使用经度的中点

# 角度转换常数
R2D = 180 / np.pi
D2R = np.pi / 180

# 计算两点间的角度（用于计算朝向目标的航向）
def calculate_target_heading(current_x: float, current_y: float, target_x: float, target_y: float) -> float:
    """计算从当前位置指向目标位置的航向角。
    
    Args:
        current_x: 当前x坐标
        current_y: 当前y坐标
        target_x: 目标x坐标
        target_y: 目标y坐标
    
    Returns:
        float: 航向角(弧度)
    """
    dx = target_x - current_x
    dy = target_y - current_y
    return np.arctan2(dy, dx)

# 计算维持编队形状的目标点
def calculate_formation_target(current_x, current_y, formation_center_x, formation_center_y, 
                              target_x, target_y, initial_dx, initial_dy, formation_weight):
    # 计算编队中心到目标的方向向量
    center_to_target_dx = target_x - formation_center_x
    center_to_target_dy = target_y - formation_center_y
    center_to_target_dist = np.sqrt(center_to_target_dx**2 + center_to_target_dy**2)
    
    if center_to_target_dist > 0:
        # 单位向量
        unit_dx = center_to_target_dx / center_to_target_dist
        unit_dy = center_to_target_dy / center_to_target_dist
    else:
        unit_dx, unit_dy = 0, 0
    
    # 计算AUV应该保持的相对位置（在编队中心坐标系中）
    formation_target_x = target_x + initial_dx * formation_weight
    formation_target_y = target_y + initial_dy * formation_weight
    
    # 综合考虑目标和编队位置
    return formation_target_x, formation_target_y

# 柔性多体动力学相关函数
def create_formation_graph(num_auvs, topology_type='hierarchical'):
    """创建编队拓扑结构图，支持多层次异构连接"""
    G = nx.Graph()
    
    # 添加节点
    for i in range(num_auvs):
        G.add_node(i)
    
    # 根据拓扑类型添加边
    if topology_type == 'complete':
        # 完全图 - 每个AUV与其他所有AUV相连
        for i in range(num_auvs):
            for j in range(i+1, num_auvs):
                G.add_edge(i, j, type='global', weight=0.5)
    
    elif topology_type == 'ring':
        # 环形 - 每个AUV只与相邻的两个AUV相连
        for i in range(num_auvs):
            G.add_edge(i, (i+1) % num_auvs, type='local', weight=1.0)
    
    elif topology_type == 'star':
        # 星形 - 中心AUV与所有其他AUV相连
        for i in range(1, num_auvs):
            G.add_edge(0, i, type='global', weight=1.0)
    
    elif topology_type == 'mesh':
        # 网格 - 每个AUV与最近的几个AUV相连
        for i in range(num_auvs):
            for j in range(i+1, min(i+4, num_auvs)):
                G.add_edge(i, j, type='local', weight=1.0)
            if i >= 3:  # 添加一些交叉连接
                G.add_edge(i, i-3, type='diagonal', weight=0.7)
    
    elif topology_type == 'hierarchical':
        # 分层次结构 - 结合局部强连接和全局弱连接
        
        # 1. 添加强局部环形连接
        for i in range(num_auvs):
            G.add_edge(i, (i+1) % num_auvs, type='local', weight=1.0)
            
        # 2. 添加次强对角连接
        for i in range(num_auvs):
            G.add_edge(i, (i+2) % num_auvs, type='diagonal', weight=0.7)
            
        # 3. 添加弱全局连接
        for i in range(num_auvs):
            for j in range(i+3, i+int(num_auvs/2), 2):
                G.add_edge(i, j % num_auvs, type='global', weight=0.3)
                
        # 4. 为高度连接性，确保至少一个远距离连接
        for i in range(0, num_auvs, 2):
            opposite = (i + num_auvs//2) % num_auvs
            G.add_edge(i, opposite, type='global', weight=0.4)
    
    elif topology_type == 'adaptive':
        # 自适应拓扑 - 基础连接为局部环形，然后根据需要动态调整
        # 初始连接为环形+少量全局连接
        for i in range(num_auvs):
            G.add_edge(i, (i+1) % num_auvs, type='local', weight=1.0)
            
        # 添加几个跨距离连接以增强鲁棒性
        for i in range(0, num_auvs, 3):
            G.add_edge(i, (i + num_auvs//2) % num_auvs, type='global', weight=0.4)
    
    # 打印拓扑统计
    local_edges = sum(1 for _, _, d in G.edges(data=True) if d.get('type') == 'local')
    diagonal_edges = sum(1 for _, _, d in G.edges(data=True) if d.get('type') == 'diagonal')
    global_edges = sum(1 for _, _, d in G.edges(data=True) if d.get('type') == 'global')
    
    print(f"拓扑结构: {topology_type}, 总连接数: {G.number_of_edges()}")
    print(f"局部连接: {local_edges}, 对角连接: {diagonal_edges}, 全局连接: {global_edges}")
    
    return G

def get_adaptive_stiffness(formation_graph, relative_position, current_strength, base_stiffness=200.0, current_threshold=0.5):
    """
    根据拓扑结构、相对位置和海流强度调整弹簧刚度
    
    参数:
    formation_graph - 表示AUV连接关系的图
    relative_position - 相对位置
    current_strength - 海流强度
    base_stiffness - 基础刚度系数
    current_threshold - 海流强度阈值
    
    返回:
    connection_stiffness - 边的刚度字典
    """
    # 初始化连接刚度字典
    connection_stiffness = {}
    
    # 获取基本刚度参数
    k_local = base_stiffness * 1.5  # 局部连接刚度增强
    k_diagonal = base_stiffness * 1.0  # 对角连接基准刚度
    k_global = base_stiffness * 0.7  # 全局连接刚度降低
    
    # 遍历所有边，根据类型设置基础刚度
    for i, j, data in formation_graph.edges(data=True):
        conn_type = data.get('type', 'local')
        weight = data.get('weight', 1.0)
        
        if conn_type == 'local':
            conn_stiffness = k_local * weight
        elif conn_type == 'diagonal':
            conn_stiffness = k_diagonal * weight
        else:  # global
            conn_stiffness = k_global * weight
        
        # 根据当前海流强度调整刚度
        # 当海流强，增加刚度以抵抗形变；当海流弱，降低刚度增加灵活性
        current_factor = 1.0 + max(0, current_strength - current_threshold) / current_threshold
        conn_stiffness *= current_factor
        
        # 存储边刚度
        connection_stiffness[(i, j)] = conn_stiffness
        connection_stiffness[(j, i)] = conn_stiffness  # 确保无向图对称性
    
    return connection_stiffness

def calculate_velocity_consensus(velocities, formation_graph, consensus_gain=0.15):
    """
    计算速度共识项，使AUV群体速度趋于一致
    
    参数:
    velocities - AUV速度列表
    formation_graph - 表示AUV连接关系的图
    consensus_gain - 共识增益系数
    
    返回:
    consensus_forces - 每个AUV的共识力
    """
    num_auvs = len(velocities)
    consensus_forces = [np.zeros(3) for _ in range(num_auvs)]
    
    # 计算平均速度（全局共识目标）
    mean_velocity = np.mean(velocities, axis=0)
    
    # 计算每个AUV的速度与邻居平均速度的差异
    for i in range(num_auvs):
        # 获取邻居节点
        neighbors = list(formation_graph.neighbors(i))
        
        if neighbors:
            # 计算邻居平均速度
            neighbor_velocities = [velocities[j] for j in neighbors]
            neighbor_mean = np.mean(neighbor_velocities, axis=0)
            
            # 加权混合局部共识(邻居平均)和全局共识(整体平均)
            local_diff = neighbor_mean - velocities[i]
            global_diff = mean_velocity - velocities[i]
            
            # 速度共识力 (70% 局部 + 30% 全局)
            consensus_forces[i] = consensus_gain * (0.7 * local_diff + 0.3 * global_diff)
    
    return consensus_forces

def calculate_spring_damper_forces(positions, velocities, initial_formation, formation_graph, 
                                  spring_stiffness, damping_coefficient, current_vectors=None):
    """
    计算虚拟弹簧和阻尼力，增强型自适应版本
    
    参数:
    positions - AUV位置列表，每个元素为[x, y, z]
    velocities - AUV速度列表，每个元素为[u, v, w]
    initial_formation - 初始编队相对位置
    formation_graph - 表示AUV连接关系的图
    spring_stiffness - 弹簧刚度系数k (基础值)
    damping_coefficient - 阻尼系数c (基础值)
    current_vectors - 海流向量列表 (可选)
    
    返回:
    flexible_forces - 每个AUV的柔性力，列表，每个元素为[Fx, Fy, Fz]
    spring_energies - 每对连接的弹簧势能
    damping_powers - 每对连接的阻尼功率
    """
    num_auvs = len(positions)
    flexible_forces = [np.zeros(3) for _ in range(num_auvs)]
    spring_energies = {}
    damping_powers = {}
    
    # 计算编队中心
    center_pos = np.mean(positions, axis=0)
    
    # 如果提供了海流数据，计算平均海流强度
    current_strength = 0.0
    if current_vectors is not None:
        current_norms = [np.linalg.norm(cv) for cv in current_vectors]
        current_strength = np.mean(current_norms)
    
    # 获取自适应刚度
    connection_stiffness = get_adaptive_stiffness(formation_graph, initial_formation, 
                                                current_strength, spring_stiffness)
    
    # 计算速度共识力
    consensus_forces = calculate_velocity_consensus(velocities, formation_graph)
    
    # 遍历所有边（AUV间的连接）
    for i, j, edge_data in formation_graph.edges(data=True):
        # 相对位置向量
        r_ij = positions[i] - positions[j]
        r_norm = np.linalg.norm(r_ij)
        
        # 计算期望距离（根据初始编队形状）
        initial_pos_i = initial_formation[i]
        initial_pos_j = initial_formation[j]
        desired_dist = np.linalg.norm(initial_pos_i - initial_pos_j)
        
        # 单位方向向量
        if r_norm > 0:
            r_unit = r_ij / r_norm
        else:
            r_unit = np.array([0, 0, 0])
        
        # 相对速度向量
        v_ij = velocities[i] - velocities[j]
        
        # 获取该连接的自适应刚度
        edge_stiffness = connection_stiffness.get((i,j), spring_stiffness)
        
        # 连接类型与权重
        conn_type = edge_data.get('type', 'local')
        weight = edge_data.get('weight', 1.0)
        
        # 根据连接类型调整阻尼系数
        edge_damping = damping_coefficient
        if conn_type == 'local':
            edge_damping *= 1.2  # 局部连接增强阻尼
        elif conn_type == 'global':
            edge_damping *= 0.8  # 全局连接降低阻尼
        
        # 计算弹簧力（增强型Hooke定律）
        # 1. 线性项 - 经典Hooke定律
        linear_term = -edge_stiffness * (r_norm - desired_dist)
        
        # 2. 非线性项 - 大变形抵抗（队形稳定性增强）
        deform_ratio = abs(r_norm - desired_dist) / desired_dist
        nonlinear_term = 0
        if deform_ratio > 0.3:  # 超过30%变形启用非线性增强
            nonlinear_term = -edge_stiffness * (deform_ratio - 0.3)**2 * np.sign(r_norm - desired_dist)
        
        # 合成弹簧力
        spring_force = (linear_term + nonlinear_term) * r_unit * weight
        
        # 计算阻尼力（Rayleigh阻尼）
        damping_force = -edge_damping * v_ij * weight
        
        # 合成柔性力
        flexible_force = spring_force + damping_force
        
        # 力的作用与反作用
        flexible_forces[i] += flexible_force
        flexible_forces[j] -= flexible_force
        
        # 计算弹簧势能
        spring_energy = 0.5 * edge_stiffness * (r_norm - desired_dist)**2 * weight
        spring_energies[(i, j)] = spring_energy
        
        # 计算阻尼功率（负值表示能量耗散）
        damping_power = edge_damping * np.linalg.norm(v_ij)**2 * weight
        damping_powers[(i, j)] = damping_power
    
    # 添加速度共识力到总柔性力
    for i in range(num_auvs):
        flexible_forces[i] += consensus_forces[i]
    
    return flexible_forces, spring_energies, damping_powers

def calculate_energy_metrics(positions, velocities, flexible_forces, spring_energies, damping_powers, 
                           thrust_forces, current_vectors, mass=500):
    """
    计算能量分析指标
    
    参数:
    positions - AUV位置列表
    velocities - AUV速度列表
    flexible_forces - 柔性力列表
    spring_energies - 弹簧势能字典
    damping_powers - 阻尼功率字典
    thrust_forces - 推力列表
    current_vectors - 海流向量列表
    mass - AUV质量
    
    返回:
    energy_metrics - 包含各种能量指标的字典
    """
    num_auvs = len(positions)
    
    # 计算每个AUV的动能
    kinetic_energies = [0.5 * mass * np.linalg.norm(v)**2 for v in velocities]
    
    # 计算总动能
    kinetic_energy = sum(kinetic_energies)
    
    # 计算弹簧总势能
    total_spring_energy = sum(spring_energies.values())
    
    # 计算阻尼总功率
    total_damping_power = sum(damping_powers.values())
    
    # 计算每个AUV的推力做功
    thrust_powers = [np.dot(thrust_forces[i], velocities[i]) for i in range(num_auvs)]
    thrust_power = sum(thrust_powers)
    
    # 计算每个AUV的海流能量交换
    current_powers = [mass * np.dot(current_vectors[i], velocities[i]) for i in range(num_auvs)]
    current_power = sum(current_powers)
    
    # 计算柔性力做功
    flex_powers = [np.dot(flexible_forces[i], velocities[i]) for i in range(num_auvs)]
    flex_power = sum(flex_powers)
    
    # 系统能量变化率
    energy_rate = thrust_power + current_power - total_damping_power
    
    # 返回所有能量指标
    energy_metrics = {
        'kinetic_energy': kinetic_energy,
        'kinetic_energies': kinetic_energies,
        'potential_energy': total_spring_energy,
        'damping_power': total_damping_power,
        'thrust_power': thrust_power,
        'thrust_powers': thrust_powers,
        'current_power': current_power,
        'current_powers': current_powers,
        'flex_power': flex_power,
        'energy_rate': energy_rate,
        'total_power': energy_rate
    }
    
    return energy_metrics

# 状态保存函数
def save_simulation_state(filename, step, T, all_Y, current_states, formation_center_x, formation_center_y, 
                         reached_target, target_reached_time, all_current_effects, initial_formation, 
                         final_formation, energy_data=None):
    import pickle
    state = {
        'step': step,
        'T': T,
        'all_Y': all_Y,
        'current_states': current_states,
        'formation_center_x': formation_center_x,
        'formation_center_y': formation_center_y,
        'reached_target': reached_target,
        'target_reached_time': target_reached_time,
        'all_current_effects': all_current_effects,
        'initial_formation': initial_formation,
        'final_formation': final_formation,
        'energy_data': energy_data  # 添加能量数据
    }
    with open(filename, 'wb') as f:
        pickle.dump(state, f)
    print(f"保存仿真状态到 {filename}")

# 创建输出目录函数
def create_output_directory():
    # 创建一个基于日期时间的输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"simulation_output_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

# 状态加载函数
def load_simulation_state(filename):
    import pickle
    with open(filename, 'rb') as f:
        state = pickle.load(f)
    print(f"从 {filename} 加载仿真状态")
    return state

# 添加三维海流与AUV可视化函数
def visualize_3d_ocean_flow(all_Y, all_current_effects, T, uo, vo, lat, lon, depth, output_dir):
    """
    创建三维海流场和AUV轨迹的可视化，使用类似于示例图片的体积可视化方法
    
    参数:
    all_Y - AUV状态历史数据
    all_current_effects - 记录的海流影响数据
    T - 时间数组
    uo, vo - 海流U和V分量
    lat, lon, depth - 网格坐标
    output_dir - 输出目录
    """
    print("开始生成三维海流和AUV轨迹可视化...")
    
    # 设置图表大小和分辨率
    plt.figure(figsize=(14, 10), dpi=150)
    
    # 创建3D图形
    ax = plt.subplot(111, projection='3d')
    
    # 限制海流场的地理范围到指定区域
    # 北纬19°-20°，东经115°-116°
    lat_min_target = 19.0
    lat_max_target = 20.0
    lon_min_target = 115.0
    lon_max_target = 116.0
    
    # 找到对应的数据索引
    lat_mask = (lat >= lat_min_target) & (lat <= lat_max_target)
    lon_mask = (lon >= lon_min_target) & (lon <= lon_max_target)
    
    # 提取指定范围内的数据
    lat_filtered = lat[lat_mask]
    lon_filtered = lon[lon_mask]
    uo_filtered = uo[:, lat_mask, :][:, :, lon_mask]
    vo_filtered = vo[:, lat_mask, :][:, :, lon_mask]
    
    print(f"原始数据范围: 纬度{lat.min():.1f}°-{lat.max():.1f}°, 经度{lon.min():.1f}°-{lon.max():.1f}°")
    print(f"过滤后数据范围: 纬度{lat_filtered.min():.1f}°-{lat_filtered.max():.1f}°, 经度{lon_filtered.min():.1f}°-{lon_filtered.max():.1f}°")
    print(f"过滤后数据形状: uo_filtered={uo_filtered.shape}, vo_filtered={vo_filtered.shape}")
    
    # 创建体积数据网格 - 使用过滤后的数据
    # 为了减少计算量，我们对数据进行降采样
    depth_sample = slice(None)  # 保留所有深度层
    lat_sample = slice(None, None, 1)  # 每1个点取1个（保留更多细节）
    lon_sample = slice(None, None, 1)  # 每1个点取1个（保留更多细节）
    
    # 获取采样后的索引数组，用于确定切片位置
    depth_indices = np.arange(len(depth))
    lat_indices = np.arange(len(lat_filtered))[lat_sample]
    lon_indices = np.arange(len(lon_filtered))[lon_sample]
    
    print(f"形状信息: depth={len(depth)}, lat_filtered={len(lat_indices)}, lon_filtered={len(lon_indices)}")
    print(f"实际深度范围: {depth.min():.1f}m ~ {depth.max():.1f}m")
    print(f"实际纬度范围: {lat_filtered.min():.3f}° ~ {lat_filtered.max():.3f}°")
    print(f"实际经度范围: {lon_filtered.min():.3f}° ~ {lon_filtered.max():.3f}°")
    
    # 准备海流数据 - 使用过滤后的数据
    uo_sampled = uo_filtered[:, lat_sample, lon_sample]  # 形状应为 (depth, lat_sampled, lon_sampled)
    vo_sampled = vo_filtered[:, lat_sample, lon_sample]
    
    # 检查数据形状
    print(f"海流数据形状: uo_sampled={uo_sampled.shape}, vo_sampled={vo_sampled.shape}")
    
    # 构建网格 - 使用实际的地理坐标
    # 注意：meshgrid的索引顺序是按照传入参数的顺序
    z_mesh, y_mesh, x_mesh = np.meshgrid(
        depth,  # 深度坐标 (m)
        lat_filtered[lat_sample],  # 纬度坐标 (°N)
        lon_filtered[lon_sample],  # 经度坐标 (°E)
        indexing='ij'
    )
    
    print(f"网格形状: x_mesh={x_mesh.shape}, y_mesh={y_mesh.shape}, z_mesh={z_mesh.shape}")
    
    # 计算海流强度(速度大小)，用于颜色映射
    current_magnitude = np.sqrt(uo_sampled**2 + vo_sampled**2)
    
    # 创建颜色映射，与示例图像匹配
    cmap = plt.cm.RdBu_r
    norm = Normalize(vmin=-0.1, vmax=0.1)  # 与示例图片相同的范围
    
    # 创建切片索引，确保覆盖整个深度范围
    depth_slices = [0, len(depth)//6, len(depth)//3, len(depth)//2, 2*len(depth)//3, 5*len(depth)//6, -1]
    if depth_slices[-1] == -1:
        depth_slices[-1] = len(depth) - 1
    
    lon_slices = [0, len(lon_indices)//4, len(lon_indices)//2, 3*len(lon_indices)//4, -1]
    if lon_slices[-1] == -1:
        lon_slices[-1] = len(lon_indices) - 1
    
    lat_slices = [0, len(lat_indices)//4, len(lat_indices)//2, 3*len(lat_indices)//4, -1]
    if lat_slices[-1] == -1:
        lat_slices[-1] = len(lat_indices) - 1
    
    # 打印切片索引信息，用于调试
    print(f"深度切片索引: {depth_slices}")
    print(f"经度切片索引: {lon_slices}")
    print(f"纬度切片索引: {lat_slices}")
    
    # 绘制海流体积切片
    print("绘制海流体积切片...")
    
    # 绘制恒定x面上的切片 (经度切片)
    for i_idx, i_slice in enumerate(lon_slices):
        if i_slice >= x_mesh.shape[2]:  # 确保索引不越界
            print(f"跳过经度切片 {i_slice}，超出范围 {x_mesh.shape[2]}")
            continue
            
        try:
            # 使用实际的地理坐标
            x_val = np.mean(x_mesh[:,:,i_slice])  # 经度值
            y_vals = y_mesh[:,:,i_slice]  # 纬度网格
            z_vals = z_mesh[:,:,i_slice]  # 深度网格
            c_vals = uo_sampled[:,:,i_slice]  # 东向流速度
            
            # 确保所有数组形状一致
            if y_vals.shape != z_vals.shape or y_vals.shape != c_vals.shape:
                print(f"形状不匹配: y_vals={y_vals.shape}, z_vals={z_vals.shape}, c_vals={c_vals.shape}")
                continue
                
            # 绘制切片 - 反转深度方向以匹配AUV轨迹
            surf = ax.plot_surface(
                np.ones_like(y_vals) * x_val, y_vals, -z_vals,
                facecolors=cmap(norm(c_vals)),
                shade=False, alpha=0.7, antialiased=True
            )
            print(f"绘制经度切片 {i_slice} (经度={x_val:.3f}°) 成功")
        except Exception as e:
            print(f"绘制经度切片 {i_slice} 出错: {e}")
    
    # 绘制恒定y面上的切片 (纬度切片)
    for j_idx, j_slice in enumerate(lat_slices):
        if j_slice >= y_mesh.shape[1]:  # 确保索引不越界
            print(f"跳过纬度切片 {j_slice}，超出范围 {y_mesh.shape[1]}")
            continue
            
        try:
            # 使用实际的地理坐标
            y_val = np.mean(y_mesh[:,j_slice,:])  # 纬度值
            x_vals = x_mesh[:,j_slice,:]  # 经度网格
            z_vals = z_mesh[:,j_slice,:]  # 深度网格
            c_vals = vo_sampled[:,j_slice,:]  # 北向流速度
            
            # 确保所有数组形状一致
            if x_vals.shape != z_vals.shape or x_vals.shape != c_vals.shape:
                print(f"形状不匹配: x_vals={x_vals.shape}, z_vals={z_vals.shape}, c_vals={c_vals.shape}")
                continue
                
            # 绘制切片 - 反转深度方向以匹配AUV轨迹
            surf = ax.plot_surface(
                x_vals, np.ones_like(x_vals) * y_val, -z_vals,
                facecolors=cmap(norm(c_vals)),
                shade=False, alpha=0.7, antialiased=True
            )
            print(f"绘制纬度切片 {j_slice} (纬度={y_val:.3f}°) 成功")
        except Exception as e:
            print(f"绘制纬度切片 {j_slice} 出错: {e}")
    
    # 绘制水平切片(恒定深度) - 增加更多切片以覆盖整个深度范围
    for k_idx, k_slice in enumerate(depth_slices):
        if k_slice >= z_mesh.shape[0]:  # 确保索引不越界
            print(f"跳过深度切片 {k_slice}，超出范围 {z_mesh.shape[0]}")
            continue
            
        try:
            # 使用实际的地理坐标
            z_val = np.mean(z_mesh[k_slice,:,:])  # 深度值
            x_vals = x_mesh[k_slice,:,:]  # 经度网格
            y_vals = y_mesh[k_slice,:,:]  # 纬度网格
            
            # 综合东向流和北向流，使用海流强度作为颜色
            c_vals = current_magnitude[k_slice,:,:]
            
            # 确保所有数组形状一致
            if x_vals.shape != y_vals.shape or x_vals.shape != c_vals.shape:
                print(f"形状不匹配: x_vals={x_vals.shape}, y_vals={y_vals.shape}, c_vals={c_vals.shape}")
                continue
                
            # 绘制切片 - 反转深度方向以匹配AUV轨迹
            surf = ax.plot_surface(
                x_vals, y_vals, np.ones_like(x_vals) * (-z_val),
                facecolors=cmap(norm(c_vals)),
                shade=False, alpha=0.7, antialiased=True
            )
            print(f"绘制深度切片 {k_slice} (深度={z_val:.1f}m) 成功")
        except Exception as e:
            print(f"绘制深度切片 {k_slice} 出错: {e}")
    
    # 添加颜色条
    m = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
    m.set_array([])
    cbar = plt.colorbar(m, ax=ax, pad=0.1)
    cbar.set_label('Velocity (m/s)', fontsize=12)
    
    # 获取AUV颜色
    num_auvs = all_Y.shape[0]
    auv_colors = get_cmap('rainbow')(np.linspace(0, 1, num_auvs))
    
    # 采样AUV轨迹点，避免过多数据点
    sample_rate = max(1, len(T) // 100)
    
    # 计算AUV轨迹的实际地理坐标
    # 使用初始位置和相对位移计算实际经纬度
    lat0 = (lat[0] + lat[-1]) / 2  # 初始纬度
    lon0 = (lon[0] + lon[-1]) / 2  # 初始经度
    
    # 绘制AUV轨迹
    for j in range(num_auvs):
        # 将AUV的x,y坐标转换为实际经纬度
        x_km = all_Y[j, ::sample_rate, 5] / 1000.0  # x坐标(km)
        y_km = all_Y[j, ::sample_rate, 6] / 1000.0  # y坐标(km)
        z_m = all_Y[j, ::sample_rate, 7]  # 深度(m)，注意z是负的，但我们要显示向上运动
        
        # 转换为经纬度坐标
        # 1度纬度约等于111km
        # 1度经度约等于111*cos(纬度)km
        lat_traj = lat0 + y_km / 111.0
        lon_traj = lon0 + x_km / (111.0 * np.cos(np.radians(lat0)))
        
        # 绘制AUV轨迹 - 反转深度方向，使AUV向上运动
        ax.plot3D(lon_traj, lat_traj, -z_m, color=auv_colors[j], linewidth=2, label=f'AUV{j+1}')
        
        # 标记起点和终点
        ax.scatter(lon_traj[0], lat_traj[0], -z_m[0], color=auv_colors[j], marker='^', s=100)
        ax.scatter(lon_traj[-1], lat_traj[-1], -z_m[-1], color=auv_colors[j], marker='o', s=100)
    
    # 设置坐标轴标签和标题
    ax.set_xlabel('Longitude (°E)', fontsize=12)
    ax.set_ylabel('Latitude (°N)', fontsize=12)
    ax.set_zlabel('Depth (m)', fontsize=12)
    ax.set_title('3D Ocean Current Velocity Field with AUV Trajectories', fontsize=16)
    
    # 添加范围信息文本框 - 使用实际的海流场范围
    plt.figtext(0.5, 0.01, 
               f'范围: 北纬 {lat_filtered.min():.1f}° - 南纬 {lat_filtered.max():.1f}°, 西经 {lon_filtered.min():.1f}° - 东经 {lon_filtered.max():.1f}°', 
               ha='center', fontsize=12, 
               bbox=dict(facecolor='white', alpha=0.8, boxstyle='round,pad=0.5'))
    
    # 添加图例
    ax.legend(loc='upper right', fontsize=10)
    
    # 设置坐标轴范围 - 使用实际的海流场范围
    ax.set_xlim([lon_filtered.min(), lon_filtered.max()])
    ax.set_ylim([lat_filtered.min(), lat_filtered.max()])
    # 深度轴范围：从深到浅，使AUV轨迹向上运动
    ax.set_zlim([-depth.max(), -depth.min()])
    
    # 调整视角以获得最佳观察效果
    ax.view_init(elev=25, azim=30)
    
    # 紧凑布局
    plt.tight_layout()
    
    # 保存图像
    visualization_path = os.path.join(output_dir, '3d_ocean_current_field.png')
    plt.savefig(visualization_path, dpi=300, bbox_inches='tight')
    print(f"三维可视化图已保存至: {visualization_path}")
    
    # 创建动画（简化版）
    if len(T) > 100:  # 只有当有足够的数据点时才创建动画
        try:
            print("开始生成AUV穿越海流动画...")
            
            # 创建新的图形用于动画
            fig_anim = plt.figure(figsize=(12, 10))
            ax_anim = fig_anim.add_subplot(111, projection='3d')
            
            # 添加简化版的海流体积可视化 - 使用过滤后的数据
            # 只取几个关键切片以避免过度绘制
            
            # 添加一个水平切片 (表层)
            try:
                k_slice = 0  # 表层
                if k_slice < z_mesh.shape[0]:
                    x_vals = x_mesh[k_slice,:,:]
                    y_vals = y_mesh[k_slice,:,:]
                    z_val = np.mean(z_mesh[k_slice,:,:])
                    c_vals = current_magnitude[k_slice,:,:]
                    
                    surf = ax_anim.plot_surface(
                        x_vals, y_vals, np.ones_like(x_vals) * (-z_val),
                        facecolors=cmap(norm(c_vals)),
                        shade=False, alpha=0.4, antialiased=True
                    )
            except Exception as e:
                print(f"动画水平切片绘制出错: {e}")
            
            # 添加一个中间深度切片
            try:
                k_slice = len(depth) // 2  # 中间深度
                if k_slice < z_mesh.shape[0]:
                    x_vals = x_mesh[k_slice,:,:]
                    y_vals = y_mesh[k_slice,:,:]
                    z_val = np.mean(z_mesh[k_slice,:,:])
                    c_vals = current_magnitude[k_slice,:,:]
                    
                    surf = ax_anim.plot_surface(
                        x_vals, y_vals, np.ones_like(x_vals) * (-z_val),
                        facecolors=cmap(norm(c_vals)),
                        shade=False, alpha=0.4, antialiased=True
                    )
            except Exception as e:
                print(f"动画中间深度切片绘制出错: {e}")
            
            # 添加一个纬度切片 (前侧)
            try:
                j_slice = 0  # 前侧
                if j_slice < y_mesh.shape[1]:
                    y_val = np.mean(y_mesh[:,j_slice,:])
                    x_vals = x_mesh[:,j_slice,:]
                    z_vals = z_mesh[:,j_slice,:]
                    c_vals = vo_sampled[:,j_slice,:]
                    
                    surf = ax_anim.plot_surface(
                        x_vals, np.ones_like(x_vals) * y_val, -z_vals,
                        facecolors=cmap(norm(c_vals)),
                        shade=False, alpha=0.4, antialiased=True
                    )
            except Exception as e:
                print(f"动画纬度切片绘制出错: {e}")
                
            # 添加一个经度切片 (侧面)
            try:
                i_slice = 0  # 侧面
                if i_slice < x_mesh.shape[2]:
                    x_val = np.mean(x_mesh[:,:,i_slice])
                    y_vals = y_mesh[:,:,i_slice]
                    z_vals = z_mesh[:,:,i_slice]
                    c_vals = uo_sampled[:,:,i_slice]
                    
                    surf = ax_anim.plot_surface(
                        np.ones_like(y_vals) * x_val, y_vals, -z_vals,
                        facecolors=cmap(norm(c_vals)),
                        shade=False, alpha=0.4, antialiased=True
                    )
            except Exception as e:
                print(f"动画经度切片绘制出错: {e}")
            
            # 添加颜色条
            m_anim = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
            m_anim.set_array([])
            cbar_anim = plt.colorbar(m_anim, ax=ax_anim)
            cbar_anim.set_label('Velocity (m/s)', fontsize=12)
            
            # 绘制完整轨迹作为背景
            for j in range(num_auvs):
                x_km = all_Y[j, ::sample_rate, 5] / 1000.0
                y_km = all_Y[j, ::sample_rate, 6] / 1000.0
                z_m = all_Y[j, ::sample_rate, 7]
                
                # 转换为经纬度坐标
                lat_traj = lat0 + y_km / 111.0
                lon_traj = lon0 + x_km / (111.0 * np.cos(np.radians(lat0)))
                
                ax_anim.plot3D(lon_traj, lat_traj, -z_m, color=auv_colors[j], linewidth=1, alpha=0.3)
            
            # 选择关键帧数量
            num_frames = min(100, len(T))
            frame_indices = np.linspace(0, len(T)-1, num_frames, dtype=int)
            
            # 初始化AUV点对象
            auv_points = []
            for j in range(num_auvs):
                point, = ax_anim.plot([], [], [], 'o', color=auv_colors[j], markersize=8)
                auv_points.append(point)
            
            # 设置坐标轴标签和标题
            ax_anim.set_xlabel('Longitude (°E)', fontsize=12)
            ax_anim.set_ylabel('Latitude (°N)', fontsize=12)
            ax_anim.set_zlabel('Depth (m)', fontsize=12)
            ax_anim.set_title('AUVs Moving Through 3D Ocean Current Field', fontsize=14)
            
            # 设置轴范围 - 使用实际的海流场范围
            ax_anim.set_xlim([lon_filtered.min(), lon_filtered.max()])
            ax_anim.set_ylim([lat_filtered.min(), lat_filtered.max()])
            # 深度轴范围：从深到浅，使AUV轨迹向上运动
            ax_anim.set_zlim([-depth.max(), -depth.min()])
            
            # 设置最佳视角
            ax_anim.view_init(elev=25, azim=30)
            
            # 动画更新函数
            def update(frame):
                idx = frame_indices[frame]
                for j, point in enumerate(auv_points):
                    x_km = all_Y[j, idx, 5] / 1000.0
                    y_km = all_Y[j, idx, 6] / 1000.0
                    z_m = all_Y[j, idx, 7]
                    
                    # 转换为经纬度坐标
                    lat_pos = lat0 + y_km / 111.0
                    lon_pos = lon0 + x_km / (111.0 * np.cos(np.radians(lat0)))
                    
                    point.set_data([lon_pos], [lat_pos])
                    point.set_3d_properties([-z_m])
                
                # 每次更新时动态调整视角，使动画更有趣
                if frame % 10 == 0:  # 每10帧调整一次视角
                    curr_elev, curr_azim = ax_anim.elev, ax_anim.azim
                    ax_anim.view_init(elev=curr_elev + 1, azim=curr_azim + 2)
                    
                return auv_points
            
            # 创建动画
            anim = animation.FuncAnimation(
                fig_anim, update, frames=num_frames, 
                interval=100, blit=True
            )
            
            # 保存动画 - 尝试使用不同的writer
            animation_path = os.path.join(output_dir, '3d_ocean_current_animation.mp4')
            try:
                writer = animation.FFMpegWriter(fps=15, metadata=dict(artist='AUV Simulation'), bitrate=1800)
                anim.save(animation_path, writer=writer)
            except Exception as e:
                print(f"使用FFMpegWriter保存动画失败: {e}")
                try:
                    # 尝试使用其他writer
                    print("尝试使用其他writer保存...")
                    animation_path = os.path.join(output_dir, '3d_ocean_current_animation.gif')
                    anim.save(animation_path, writer='pillow', fps=10, dpi=100)
                except Exception as e2:
                    print(f"无法保存动画: {e2}")
            
            print(f"动画已保存至: {animation_path}")
        except Exception as e:
            print(f"创建动画时发生错误: {e}")
    
    plt.show()
    
    return

# 主函数
# 定义仿真阶段枚举
class SimulationPhase:
    RANDOM_TO_CIRCULAR = 1
    CIRCULAR_TO_TARGET = 2
    TARGET_ASSIGNMENT = 3

def main_multi_phase(max_steps=80000, load_state=False, save_results=True, auto_stop=True, target_reach_distance=1.0,
         use_flexible_model=True, spring_stiffness=100.0, damping_coefficient=80.0,
         formation_topology='hierarchical', auv_mass=500.0, consensus_gain=0.2,
         data_record_interval=10, enable_3d_visualization=True):
    """
    多阶段AUV编队控制系统主函数

    阶段1: 随机分布到圆形编队
    阶段2: 圆形编队协调移动到目标区域
    阶段3: 目标区域一对一位置分配
    """
    # 参数初始化
    h = 0.05  # 时间步长 (秒)
    m = max_steps  # 可配置仿真步数
    num_auvs = 10  # AUV数量

    # 数据记录频率
    print(f"每{data_record_interval}步记录一次数据，用于加快解算速度")

    # 计算相关间隔
    formation_calc_interval = max(5, data_record_interval * 5)
    velocity_calc_interval = max(10, data_record_interval * 10)
    progress_output_interval = max(1000, data_record_interval * 200)
    detail_output_interval = progress_output_interval * 5

    print(f"编队计算间隔: {formation_calc_interval}步")
    print(f"速度一致性计算间隔: {velocity_calc_interval}步")
    print(f"进度输出间隔: {progress_output_interval}步")

    # 创建输出目录
    output_dir = create_output_directory()
    print(f"所有输出将保存到: {output_dir}")

    # 多阶段目标设置
    # 阶段1: 圆形编队中心位置 (10, 10)
    circular_center_x = 10.0
    circular_center_y = 10.0
    circular_radius = 5.0  # 圆形编队半径 (km)

    # 阶段2: 目标区域中心 (60, 80)
    target_area_x = 60.0
    target_area_y = 80.0

    # 阶段3: 预定义的11个目标位置 (10个跟随者 + 1个领导者)
    # 在目标区域(60,80)周围设置11个点
    target_positions = [
        [60.0, 80.0],   # 领导者位置 (中心)
        [58.0, 82.0],   # 跟随者1
        [62.0, 82.0],   # 跟随者2
        [56.0, 78.0],   # 跟随者3
        [64.0, 78.0],   # 跟随者4
        [58.0, 78.0],   # 跟随者5
        [62.0, 78.0],   # 跟随者6
        [60.0, 76.0],   # 跟随者7
        [60.0, 84.0],   # 跟随者8
        [56.0, 82.0],   # 跟随者9
        [64.0, 82.0],   # 跟随者10
    ]

    # 阶段管理变量
    current_phase = SimulationPhase.RANDOM_TO_CIRCULAR
    phase_start_time = 0.0
    phase_transition_times = []

    # 阶段转换条件
    circular_formation_threshold = 1.0  # 圆形编队形成阈值 (km)
    target_area_threshold = 2.0  # 到达目标区域阈值 (km)
    final_position_threshold = 0.5  # 最终位置到达阈值 (km)

    # AUV分配 (AUV 0 为领导者)
    leader_id = 0
    follower_ids = list(range(1, num_auvs))

    # 当前目标位置 (根据阶段动态更新)
    current_targets = [[0.0, 0.0] for _ in range(num_auvs)]
    auv_assigned_positions = list(range(num_auvs))  # AUV到目标位置的分配
    auv_reached_target = [False] * num_auvs  # 记录每个AUV是否到达目标

    print(f"=== 多阶段AUV编队控制系统 ===")
    print(f"阶段1: 随机分布 → 圆形编队 (中心: {circular_center_x}, {circular_center_y}, 半径: {circular_radius}km)")
    print(f"阶段2: 圆形编队 → 目标区域 ({target_area_x}, {target_area_y})")
    print(f"阶段3: 目标区域 → 一对一位置分配")
    print(f"领导者: AUV{leader_id}, 跟随者: AUV{follower_ids}")

    # 检查自动停止设置
    if auto_stop:
        print(f"自动停止功能已启用: 当所有AUV到达最终位置时将自动结束仿真")
    else:
        print("自动停止功能已禁用: 即使到达目标区域也将继续仿真直到最大步数")

    # 初始化AUV位置 - 阶段1: 适度混乱的初始分布，然后排成一字型
    print("\n=== 阶段1: 初始化适度混乱分布 ===")

    # 创建适度混乱的初始位置 - 基于一字型排列但加入随机扰动
    np.random.seed(42)  # 设置随机种子以确保可重复性
    initial_positions = []

    # 基础一字型排列参数
    line_start_x = 5.0  # 一字型起始x坐标
    line_start_y = 15.0  # 一字型起始y坐标
    line_spacing = 2.0   # AUV之间的间距
    chaos_factor = 1.5   # 混乱因子，控制随机扰动的大小

    for i in range(num_auvs):
        # 基础一字型位置
        base_x = line_start_x + i * line_spacing
        base_y = line_start_y

        # 添加随机扰动创造混乱效果
        chaos_x = np.random.uniform(-chaos_factor, chaos_factor)
        chaos_y = np.random.uniform(-chaos_factor, chaos_factor)

        x_pos = base_x + chaos_x
        y_pos = base_y + chaos_y

        initial_positions.append([x_pos, y_pos])
        print(f"AUV{i}: 初始位置 ({x_pos:.1f}, {y_pos:.1f}) km (基础一字型+混乱扰动)")

    # 计算圆形编队的目标位置
    circular_formation_targets = []
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)

    for i, angle in enumerate(angles):
        if i == leader_id:
            # 领导者位于圆心
            target_x = circular_center_x
            target_y = circular_center_y
        else:
            # 跟随者均匀分布在圆周上
            target_x = circular_center_x + circular_radius * np.cos(angle)
            target_y = circular_center_y + circular_radius * np.sin(angle)

        circular_formation_targets.append([target_x, target_y])
        print(f"AUV{i}: 圆形编队目标 ({target_x:.1f}, {target_y:.1f}) km")

    # 设置初始目标为圆形编队位置
    current_targets = circular_formation_targets.copy()

    # 柔性多体动力学参数
    if use_flexible_model:
        print("使用柔性多体动力学模型...")
        # 创建编队拓扑图
        formation_graph = create_formation_graph(num_auvs, topology_type=formation_topology)
        print(f"编队拓扑: {formation_topology}, 连接数: {formation_graph.number_of_edges()}")
    else:
        print("使用标准动力学模型...")
        formation_graph = None

    # 初始化AUV状态
    states = []
    for i in range(num_auvs):
        x_pos, y_pos = initial_positions[i]

        # 设置随机初始航向
        initial_heading = np.random.uniform(0, 2*np.pi)

        # 初始状态: [u, v, w, q, r, x_p, y_p, z_p, theta, psi]
        state = np.array([0, 0, 0, 0, 0, x_pos * 1000, y_pos * 1000, 0, 0, initial_heading])  # 转换为米
        states.append(state)

    initial_states = np.array(states)

    # 保存初始编队相对位置 (用于圆形编队)
    initial_formation = []
    for i in range(num_auvs):
        target_x, target_y = circular_formation_targets[i]
        initial_formation.append([target_x - circular_center_x, target_y - circular_center_y])
    initial_formation = np.array(initial_formation)

    # 设置不同的初始速度
    for i in range(num_auvs):
        # 每个AUV有不同的初始速度(0.8-1.2 m/s范围内)
        initial_states[i, 0] = 1.0 + np.random.uniform(-0.2, 0.2)
        # 添加小的初始横向速度
        initial_states[i, 1] = np.random.uniform(-0.1, 0.1)

    # 创建更加差异化的推力和舵角设置
    thrust_settings = np.linspace(280, 380, num_auvs)  # 扩大推力范围差异
    # 添加小的随机变化到每个推力设置
    thrust_settings = thrust_settings + np.random.uniform(-10, 10, num_auvs)

    # 垂直舵角仍保持为0(保持水平运动)
    deltah_settings = np.zeros(num_auvs)

    # 水平舵角初始有微小差异
    deltav_settings = np.random.uniform(-0.02, 0.02, num_auvs)

    # 定义目标区域半径
    target_radius = 5.0  # 目标区域半径 (km)

    # 定义编队保持参数
    formation_keep_distance = 10.0  # 开始强制保持编队形状的距离（km）

    # 为每个AUV创建状态数组
    T = np.zeros(m)
    all_Y = np.zeros((num_auvs, m, 10))  # [AUV编号, 时间步, 状态向量]

    # 为每个AUV创建当前状态向量
    current_states = initial_states.copy()

    # 记录每个AUV在每个时间步的海流影响
    all_current_effects = np.zeros((num_auvs, m, 3))  # [AUV编号, 时间步, [u_c, v_c, 海流强度]]

    # 记录编队中心位置
    formation_center_x = np.zeros(m)
    formation_center_y = np.zeros(m)

    # 初始化第一个点的编队中心位置
    formation_center_x[0] = np.mean([pos[0] for pos in initial_positions])
    formation_center_y[0] = np.mean([pos[1] for pos in initial_positions])

    # 记录编队到达目标的时间
    reached_target = False
    target_reached_time = 0

    # 记录最终编队形状
    final_formation = np.zeros_like(initial_formation)
    
    # 使用彩虹色谱为每个AUV分配不同颜色
    colors = get_cmap('rainbow')(np.linspace(0, 1, num_auvs))
    
    # 定义最大速度限制 (2.0 m/s)
    max_speed = 2.0
    
    # 选取表层海流数据
    uo_plot = uo[0]  # 表层海流 u 分量
    vo_plot = vo[0]  # 表层海流 v 分量
    
    # 创建经纬度网格
    lon_grid, lat_grid = np.meshgrid(lon, lat)
    
    # 使用pyproj计算相对坐标
    geod = Geod(ellps='WGS84')
    center_lat = (lat[0] + lat[-1]) / 2
    center_lon = (lon[0] + lon[-1]) / 2
    
    # 计算每个格点相对于中心的距离
    dx_km = np.zeros_like(lon_grid)
    dy_km = np.zeros_like(lat_grid)
    for i in range(lat_grid.shape[0]):
        for j in range(lat_grid.shape[1]):
            az12, _, dist = geod.inv(center_lon, center_lat, lon_grid[i, j], lat_grid[i, j])
            dx_km[i, j] = dist * np.sin(np.radians(az12)) / 1000
            dy_km[i, j] = dist * np.cos(np.radians(az12)) / 1000
    
    # 平移坐标原点至图左下角
    x_shift, y_shift = dx_km.min(), dy_km.min()
    dx_km -= x_shift
    dy_km -= y_shift
    
    # 初始化多个AUV (沿圆周排列)
    radius_km = 5  # 圆半径，单位公里
    
    # 在坐标系中心点附近创建圆形编队
    states = []
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)
    
    # 保存初始编队相对位置
    initial_formation = []
    
    # 设置编队中心位置
    center_x = 20  # 指定横坐标为20
    center_y = 10  # 指定纵坐标为10
    
    for angle in angles:
        # 计算圆上的点 - 圆心设置在(center_x, center_y)
        x_p = center_x + radius_km * np.cos(angle)
        y_p = center_y + radius_km * np.sin(angle)
        
        # 保存初始相对位置（相对于编队中心）
        initial_formation.append([x_p - center_x, y_p - center_y])
        
        # 设置随机初始航向，不再指向目标
        initial_heading = np.random.uniform(0, 2*np.pi)  # 随机航向
        
        # 初始状态: [u, v, w, q, r, x_p, y_p, z_p, theta, psi]
        state = np.array([0, 0, 0, 0, 0, x_p * 1000, y_p * 1000, 0, 0, initial_heading])  # 转换为米
        states.append(state)
    
    initial_states = np.array(states)
    initial_formation = np.array(initial_formation)  # 转换为NumPy数组
    
    # 设置不同的初始速度
    for i in range(num_auvs):
        # 每个AUV有不同的初始速度(0.8-1.2 m/s范围内)
        initial_states[i, 0] = 1.0 + np.random.uniform(-0.2, 0.2)  
        
        # 添加小的初始横向速度
        initial_states[i, 1] = np.random.uniform(-0.1, 0.1)
    
    # 为每个AUV创建状态数组
    T = np.zeros(m)
    all_Y = np.zeros((num_auvs, m, 10))  # [AUV编号, 时间步, 状态向量]
    
    # 为每个AUV创建当前状态向量和速度记录
    current_states = initial_states.copy()
    
    # 记录每个AUV在每个时间步的海流影响
    all_current_effects = np.zeros((num_auvs, m, 3))  # [AUV编号, 时间步, [u_c, v_c, 海流强度]]
    
    # 记录编队中心位置
    formation_center_x = np.zeros(m)
    formation_center_y = np.zeros(m)
    
    # 初始化第一个点的编队中心位置
    formation_center_x[0] = center_x
    formation_center_y[0] = center_y
    
    # 记录编队到达目标的时间
    reached_target = False
    target_reached_time = 0
    
    # 记录最终编队形状
    final_formation = np.zeros_like(initial_formation)
    
    # 保存轨迹和指标数据 - 减少数据存储量
    all_data = {
        'time': [],
        'formation_center_x': [],
        'formation_center_y': [],
        'dist_to_target': [],
        'formation_deviation': []
    }
    
    # 能量分析数据
    energy_data = {
        'time': [],
        'kinetic_energy': [],
        'potential_energy': [],
        'damping_energy': [],
        'thrust_work': [],
        'current_exchange': [],
        'total_energy': []
    }
    cumulative_energy = {
        'damping_energy': 0.0,
        'thrust_work': 0.0,
        'current_exchange': 0.0
    }
    
    # 边界设置
    x_boundary_min = 0
    x_boundary_max = dx_km.max()
    y_boundary_min = 0
    y_boundary_max = dy_km.max()
    
    # 主循环 - 时间步
    # 初始化或加载仿真状态
    start_step = 0
    if load_state:
        try:
            # 尝试加载上次仿真状态
            saved_state = load_simulation_state('simulation_state.pkl')
            start_step = saved_state['step'] + 1
            T[:start_step] = saved_state['T'][:start_step]
            all_Y[:, :start_step, :] = saved_state['all_Y'][:, :start_step, :]
            current_states = saved_state['current_states']
            formation_center_x[:start_step] = saved_state['formation_center_x'][:start_step]
            formation_center_y[:start_step] = saved_state['formation_center_y'][:start_step]
            reached_target = saved_state['reached_target']
            target_reached_time = saved_state['target_reached_time']
            all_current_effects[:, :start_step, :] = saved_state['all_current_effects'][:, :start_step, :]
            print(f"从步骤 {start_step} 继续仿真，总步数: {m}")
        except FileNotFoundError:
            print("没有找到之前的仿真状态，从头开始...")
        except Exception as e:
            print(f"加载状态失败: {e}，从头开始...")
    
    print("开始仿真...")
    start_time = time.time()

    for i in range(start_step, m):
        t = h * (i + 1)
        T[i] = t

        # 当前编队中心位置
        current_center_x = 0
        current_center_y = 0

        # 处理每个AUV
        for j in range(num_auvs):
            x = current_states[j]

            # 当前坐标（根据累积位置计算当前经纬度）
            lat_now = lat0 + x[6] / 111000
            lon_now = lon0 + x[5] / (111000 * np.cos(lat0 * np.pi / 180))
            depth_now = -x[7]  # 注意 z 是负的

            # 当前位置(km)
            current_x_km = x[5] / 1000.0
            current_y_km = x[6] / 1000.0

            # 累加用于计算编队中心
            current_center_x += current_x_km
            current_center_y += current_y_km

        # 计算编队中心
        current_center_x /= num_auvs
        current_center_y /= num_auvs

        # === 阶段管理逻辑 ===
        # 根据当前阶段确定目标位置
        if current_phase == SimulationPhase.RANDOM_TO_CIRCULAR:
            # 阶段1: 随机分布到圆形编队
            # 检查是否形成了稳定的圆形编队
            formation_error = 0.0
            for j in range(num_auvs):
                current_x_km = current_states[j][5] / 1000.0
                current_y_km = current_states[j][6] / 1000.0
                target_x, target_y = circular_formation_targets[j]
                error = np.sqrt((current_x_km - target_x)**2 + (current_y_km - target_y)**2)
                formation_error += error

            formation_error /= num_auvs  # 平均误差

            if formation_error < circular_formation_threshold:
                # 转换到阶段2
                current_phase = SimulationPhase.CIRCULAR_TO_TARGET
                phase_start_time = t
                phase_transition_times.append(t)
                print(f"\n=== 阶段转换 (t={t:.1f}s) ===")
                print(f"阶段1完成: 圆形编队形成，平均误差: {formation_error:.2f}km")
                print(f"进入阶段2: 圆形编队向目标区域移动")

                # 更新目标为目标区域中心，但保持圆形编队结构
                for j in range(num_auvs):
                    if j == leader_id:
                        current_targets[j] = [target_area_x, target_area_y]
                    else:
                        # 跟随者保持相对于领导者的圆形编队位置
                        angle = angles[j]
                        offset_x = circular_radius * np.cos(angle)
                        offset_y = circular_radius * np.sin(angle)
                        current_targets[j] = [target_area_x + offset_x, target_area_y + offset_y]

        elif current_phase == SimulationPhase.CIRCULAR_TO_TARGET:
            # 阶段2: 圆形编队移动到目标区域
            # 检查编队中心是否到达目标区域
            dist_to_target_area = np.sqrt((current_center_x - target_area_x)**2 + (current_center_y - target_area_y)**2)

            if dist_to_target_area < target_area_threshold:
                # 转换到阶段3
                current_phase = SimulationPhase.TARGET_ASSIGNMENT
                phase_start_time = t
                phase_transition_times.append(t)
                print(f"\n=== 阶段转换 (t={t:.1f}s) ===")
                print(f"阶段2完成: 编队到达目标区域，距离: {dist_to_target_area:.2f}km")
                print(f"进入阶段3: 一对一位置分配")

                # 更新目标为预定义的最终位置
                current_targets = target_positions.copy()

        elif current_phase == SimulationPhase.TARGET_ASSIGNMENT:
            # 阶段3: 一对一位置分配
            # 检查每个AUV是否到达其指定位置
            all_reached = True
            for j in range(num_auvs):
                if not auv_reached_target[j]:
                    current_x_km = current_states[j][5] / 1000.0
                    current_y_km = current_states[j][6] / 1000.0
                    target_x, target_y = current_targets[j]
                    dist_to_final = np.sqrt((current_x_km - target_x)**2 + (current_y_km - target_y)**2)

                    if dist_to_final < final_position_threshold:
                        auv_reached_target[j] = True
                        print(f"AUV{j} 到达最终位置 ({target_x:.1f}, {target_y:.1f}), 距离: {dist_to_final:.2f}km")
                    else:
                        all_reached = False

            if all_reached and auto_stop:
                print(f"\n=== 仿真完成 (t={t:.1f}s) ===")
                print("所有AUV已到达指定位置，仿真结束")
                actual_steps = i + 1
                break

        # 计算当前目标的距离 (用于编队控制)
        # 使用编队中心到当前阶段目标的距离
        if current_phase == SimulationPhase.RANDOM_TO_CIRCULAR:
            target_x, target_y = circular_center_x, circular_center_y
        elif current_phase == SimulationPhase.CIRCULAR_TO_TARGET:
            target_x, target_y = target_area_x, target_area_y
        else:  # TARGET_ASSIGNMENT
            target_x, target_y = target_area_x, target_area_y  # 使用目标区域中心作为参考

        dist_to_target = np.sqrt((current_center_x - target_x)**2 + (current_center_y - target_y)**2)
        
        # 动态调整编队保持权重 - 越接近目标，编队保持越重要
        if dist_to_target < formation_keep_distance:
            # 当接近目标时，增加编队形状保持的权重
            # 线性增加权重：从目标距离formation_keep_distance处的1.0到目标处的最大值
            formation_weight = 1.0 + (formation_keep_distance - dist_to_target) / formation_keep_distance * 1.0
        else:
            formation_weight = 1.0
            
        # 计算当前编队形状偏差
        current_formation_deviation = 0
        
        # 准备位置和速度数据用于柔性动力学计算
        positions = []
        velocities = []
        thrust_forces = []
        current_vectors = []
        
        # 处理每个AUV的导航
        for j in range(num_auvs):
            x = current_states[j]

            # 当前位置(km)
            current_x_km = x[5] / 1000.0
            current_y_km = x[6] / 1000.0

            # 收集位置和速度数据
            positions.append(np.array([x[5], x[6], x[7]]))  # [x, y, z] (单位: m)
            velocities.append(np.array([x[0], x[1], x[2]]))  # [u, v, w] (单位: m/s)
            thrust_forces.append(np.array([thrust_settings[j], 0.0, 0.0]))  # [Fu, 0, 0] (近似)
            current_vectors.append(np.array([all_current_effects[j, i, 0], all_current_effects[j, i, 1], 0.0]))  # [uc, vc, 0]

            # 计算相对于编队中心的当前位置
            current_rel_x = current_x_km - current_center_x
            current_rel_y = current_y_km - current_center_y

            # 计算与初始编队形状的偏差
            deviation = np.sqrt((current_rel_x - initial_formation[j][0])**2 +
                                (current_rel_y - initial_formation[j][1])**2)
            current_formation_deviation += deviation

            # 根据当前阶段确定目标点
            if current_phase == SimulationPhase.TARGET_ASSIGNMENT and auv_reached_target[j]:
                # 阶段3: 如果AUV已到达目标位置，则停止移动
                formation_target_x = current_x_km
                formation_target_y = current_y_km
            else:
                # 使用当前阶段的目标位置
                formation_target_x, formation_target_y = current_targets[j]

            # 计算朝向目标的航向
            target_heading = calculate_target_heading(current_x_km, current_y_km, formation_target_x, formation_target_y)
        
        # 如果使用柔性多体动力学模型，计算虚拟弹簧-阻尼力
        flexible_forces = [np.zeros(3) for _ in range(num_auvs)]
        spring_energies = {}
        damping_powers = {}
        
        if use_flexible_model and formation_graph:
            # 自适应调整参数 - 根据与目标的距离
            dist_factor = min(1.0, dist_to_target / formation_keep_distance)
            adaptive_stiffness = spring_stiffness * (1.0 + (1.0 - dist_factor))  # 接近目标时增大刚度
            adaptive_damping = damping_coefficient * (1.0 + 0.5 * (1.0 - dist_factor))  # 接近目标时增大阻尼
            
            # 检测海流扰动强度，计算自适应参数增强
            current_norms = [np.linalg.norm([all_current_effects[j, i, 0], all_current_effects[j, i, 1]]) 
                            for j in range(num_auvs)]
            avg_current = np.mean(current_norms)
            max_current = np.max(current_norms)
            
            # 在大扰动情况下增强队形刚度
            if max_current > 1.0:  # 强海流条件
                print(f"检测到强海流 ({max_current:.2f} m/s)，增强队形刚度!")
                adaptive_stiffness *= 1.5
            
            # 增加速度共识增益 - 接近目标时增大以确保协同
            adaptive_consensus_gain = consensus_gain * (1.0 + (1.0 - dist_factor) * 0.5)
            
            flexible_forces, spring_energies, damping_powers = calculate_spring_damper_forces(
                positions, velocities, initial_formation * 1000.0,  # 转换为米
                formation_graph, adaptive_stiffness, adaptive_damping,
                current_vectors=current_vectors
            )
            
            # 计算能量指标
            energy_metrics = calculate_energy_metrics(
                positions, velocities, flexible_forces, spring_energies, damping_powers,
                thrust_forces, current_vectors, mass=auv_mass
            )
            
            # 记录能量数据
            energy_data['time'].append(t)
            energy_data['kinetic_energy'].append(energy_metrics['kinetic_energy'])
            energy_data['potential_energy'].append(energy_metrics['potential_energy'])
            
            # 累积能量变化
            cumulative_energy['damping_energy'] += energy_metrics['damping_power'] * h
            cumulative_energy['thrust_work'] += energy_metrics['thrust_power'] * h
            cumulative_energy['current_exchange'] += energy_metrics['current_power'] * h
            
            # 记录累积值
            energy_data['damping_energy'].append(cumulative_energy['damping_energy'])
            energy_data['thrust_work'].append(cumulative_energy['thrust_work'])
            energy_data['current_exchange'].append(cumulative_energy['current_exchange'])
            energy_data['total_energy'].append(
                energy_metrics['kinetic_energy'] + 
                energy_metrics['potential_energy'] + 
                cumulative_energy['thrust_work'] + 
                cumulative_energy['current_exchange'] - 
                cumulative_energy['damping_energy']
            )
            
            # 分析编队速度一致性
            velocities_array = np.array(velocities)
            speeds = np.linalg.norm(velocities_array, axis=1)
            speed_std = np.std(speeds)
            speed_mean = np.mean(speeds)
            speed_cv = speed_std / speed_mean if speed_mean > 0 else 0  # 速度变异系数
            
            if i % 500 == 0:
                print(f"编队速度分析: 平均={speed_mean:.2f} m/s, 标准差={speed_std:.2f}, 变异系数={speed_cv:.2f}")
                print(f"弹簧势能: {energy_metrics['potential_energy']:.2f} J, 阻尼功率: {energy_metrics['damping_power']:.2f} W")
        
        # 处理每个AUV的动力学
        for j in range(num_auvs):
            x = current_states[j]

            # 当前位置(km)
            current_x_km = x[5] / 1000.0
            current_y_km = x[6] / 1000.0

            # 根据当前阶段确定目标点
            if current_phase == SimulationPhase.TARGET_ASSIGNMENT and auv_reached_target[j]:
                # 阶段3: 如果AUV已到达目标位置，则停止移动
                formation_target_x = current_x_km
                formation_target_y = current_y_km
            else:
                # 使用当前阶段的目标位置
                formation_target_x, formation_target_y = current_targets[j]

            # 计算朝向目标的航向
            target_heading = calculate_target_heading(current_x_km, current_y_km, formation_target_x, formation_target_y)
            
            # 检查是否接近边界，调整目标航向远离边界
            boundary_margin = 5.0  # 边界安全距离（千米）
            boundary_influence = False
            
            # 创建边界避让航向
            boundary_heading = target_heading  # 默认与目标航向相同
            
            # 检查各个边界的距离，如果接近边界则调整航向
            if current_x_km < (x_boundary_min + boundary_margin):
                # 接近左边界，向右避开
                boundary_heading = 0.0  # 正东方向
                boundary_influence = True
            elif current_x_km > (x_boundary_max - boundary_margin):
                # 接近右边界，向左避开
                boundary_heading = np.pi  # 正西方向
                boundary_influence = True
            
            if current_y_km < (y_boundary_min + boundary_margin):
                # 接近下边界，向上避开
                boundary_heading = np.pi/2  # 正北方向
                boundary_influence = True
            elif current_y_km > (y_boundary_max - boundary_margin):
                # 接近上边界，向下避开
                boundary_heading = 3*np.pi/2  # 正南方向
                boundary_influence = True
            
            # 综合考虑目标航向和边界约束，当接近边界时优先考虑边界避让航向
            if boundary_influence:
                # 如果接近边界，使用边界避让航向代替目标航向
                target_heading = boundary_heading
            
            # 边界检查：限制在数据有效范围内
            lat_margin = 0.05  # 边距
            lon_margin = 0.05
            depth_margin = 5.0
            
            # 检查是否接近边界
            is_inside = (
                depth.min() + depth_margin <= depth_now <= depth.max() - depth_margin and
                lat.min() + lat_margin <= lat_now <= lat.max() - lat_margin and
                lon.min() + lon_margin <= lon_now <= lon.max() - lon_margin
            )
            
            if not is_inside:
                # 限制在数据有效范围内
                lat_now = np.clip(lat_now, lat.min() + lat_margin, lat.max() - lat_margin)
                lon_now = np.clip(lon_now, lon.min() + lon_margin, lon.max() - lon_margin)
                depth_now = np.clip(depth_now, depth.min() + depth_margin, depth.max() - depth_margin)

            # 获取当前位置插值的海流速度
            try:
                u_c, v_c = ocean.get_current(depth_now, lat_now, lon_now)
                
                # 检查返回的海流值是否有效
                if np.isnan(u_c) or np.isnan(v_c):
                    # 尝试周围点的平均值
                    u_c_vals = []
                    v_c_vals = []
                    for d_offset in [-1, 0, 1]:
                        for lat_offset in [-0.01, 0, 0.01]:
                            for lon_offset in [-0.01, 0, 0.01]:
                                try:
                                    d = depth_now + d_offset
                                    la = lat_now + lat_offset
                                    lo = lon_now + lon_offset
                                    if (depth.min() + depth_margin <= d <= depth.max() - depth_margin and
                                        lat.min() + lat_margin <= la <= lat.max() - lat_margin and
                                        lon.min() + lon_margin <= lo <= lon.max() - lon_margin):
                                        temp_u, temp_v = ocean.get_current(d, la, lo)
                                        if not (np.isnan(temp_u) or np.isnan(temp_v)):
                                            u_c_vals.append(temp_u)
                                            v_c_vals.append(temp_v)
                                except:
                                    continue
                    
                    if u_c_vals and v_c_vals:
                        u_c = np.mean(u_c_vals)
                        v_c = np.mean(v_c_vals)
                    else:
                        u_c, v_c = 0.0, 0.0
                
                # 确保值是浮点数
                u_c = float(u_c)
                v_c = float(v_c)
                
                # 移除海流放大
                # u_c *= 10  # 删除放大因子
                # v_c *= 10  # 删除放大因子
                
                w_c = 0.0
            except Exception as e:
                print(f"海流获取错误: {e}")
                u_c, v_c, w_c = 0.0, 0.0, 0.0
            
            # 记录海流影响
            all_current_effects[j, i, 0] = u_c
            all_current_effects[j, i, 1] = v_c
            all_current_effects[j, i, 2] = np.sqrt(u_c**2 + v_c**2)
            
            # 海流三维向量
            current_vector = [u_c, v_c, w_c]
            
            # 计算海流方向和强度
            current_strength = np.sqrt(u_c**2 + v_c**2)
            if current_strength > 0:
                current_direction = np.arctan2(v_c, u_c)
            else:
                current_direction = 0.0
                
            # 计算海流方向与AUV当前航向的夹角
            heading_current_diff = AdjustAngle(current_direction - x[9])
            
            # 计算与目标航向的差值
            target_heading_diff = AdjustAngle(target_heading - x[9])
            
            # 控制输入 - 每个AUV使用不同参数
            Fu = thrust_settings[j]  # 恒定推力，每个AUV不同
            
            # 添加舵角扰动 - 将海流和导航需求结合
            # 1. 海流横向分量产生的扰动
            cross_current_effect = np.sin(heading_current_diff) * current_strength * 0.1
            
            # 2. 随机扰动大幅减弱 (减小10倍)，删除周期性波动
            if i % 3 == 0:
                random_effect = 0.04 * (np.random.randn()) * (1.0 + 0.3 * j)  # 从0.4减为0.04
            else:
                random_effect = 0.01 * (np.random.randn())  # 从0.1减为0.01
            
            # 3. 添加朝向目标的航向修正
            # 计算到目标的距离
            dist_to_target = np.sqrt((current_x_km - target_x)**2 + (current_y_km - target_y)**2)
            heading_correction = 0.3 * np.sin(target_heading_diff)  # 添加航向修正，使AUV朝向目标
            
            # 合并所有舵角扰动
            deltav = deltav_settings[j] + cross_current_effect + random_effect + heading_correction
            deltah = deltah_settings[j]
            
            # 获取柔性力（如果使用柔性模型）
            flex_force = flexible_forces[j] if use_flexible_model else np.zeros(3)
            
            # 龙格-库塔积分 - 修改为包含柔性力
            k1 = Infante_3d_with_current(t, x, [Fu, deltav, deltah], current_vector, flex_force=flex_force)
            k2 = Infante_3d_with_current(t + h / 2, x + h / 2 * k1, [Fu, deltav, deltah], current_vector, flex_force=flex_force)
            k3 = Infante_3d_with_current(t + h / 2, x + h / 2 * k2, [Fu, deltav, deltah], current_vector, flex_force=flex_force)
            k4 = Infante_3d_with_current(t + h, x + h * k3, [Fu, deltav, deltah], current_vector, flex_force=flex_force)

            x = x + h / 6 * (k1 + 2 * k2 + 2 * k3 + k4)
            
            # 状态限制 - 确保AUV不超出边界
            # 转换为千米检查边界
            x_km = x[5] / 1000.0
            y_km = x[6] / 1000.0
            
            # 距离边界的缓冲区（千米）
            buffer = 1.0
            
            # 检查是否接近边界
            if x_km <= (x_boundary_min + buffer):
                # 接近左边界，施加向右的力
                x[0] += 0.3  # 增加向右的速度
                x[5] = (x_boundary_min + buffer) * 1000  # 设置最小x位置
            elif x_km >= (x_boundary_max - buffer):
                # 接近右边界，施加向左的力
                x[0] -= 0.3  # 增加向左的速度
                x[5] = (x_boundary_max - buffer) * 1000  # 设置最大x位置
            
            if y_km <= (y_boundary_min + buffer):
                # 接近下边界，施加向上的力
                x[1] += 0.3  # 增加向上的速度
                x[6] = (y_boundary_min + buffer) * 1000  # 设置最小y位置
            elif y_km >= (y_boundary_max - buffer):
                # 接近上边界，施加向下的力
                x[1] -= 0.3  # 增加向下的速度
                x[6] = (y_boundary_max - buffer) * 1000  # 设置最大y位置
            
            # 最终位置限制
            x[5] = LimitMaxMin(x[5], (x_boundary_max - buffer/2) * 1000, (x_boundary_min + buffer/2) * 1000)  # x方向
            x[6] = LimitMaxMin(x[6], (y_boundary_max - buffer/2) * 1000, (y_boundary_min + buffer/2) * 1000)  # y方向
            x[7] = LimitMaxMin(x[7], 0, -0.5)  # z方向限制在水面以下0.5米
            
            # 更强随机速度扰动 (减弱10倍，并移除周期性波动)
            # 基础随机扰动
            base_disturbance = np.random.randn()
            
            # 海流影响因子：使扰动与海流强度相关
            current_factor = 0.03 * current_strength  # 从0.3减为0.03
            
            # AUV编号影响：不同AUV有不同的扰动模式
            auv_factor = 1.0 + 0.2 * np.sin(j * np.pi/5)
            
            # 综合随机扰动计算（完全随机，但幅度降低10倍）
            u_disturbance = 0.025 * base_disturbance * auv_factor + current_factor + 0.015 * np.random.randn()  # 从0.25减为0.025
            v_disturbance = 0.02 * np.random.randn() * auv_factor + 0.01 * current_factor  # 从0.2减为0.02
            w_disturbance = 0.008 * np.random.randn() * auv_factor  # 从0.08减为0.008
            
            # 应用扰动
            x[0] += u_disturbance
            x[1] += v_disturbance
            x[2] += w_disturbance
            
            # 计算当前合速度
            current_speed = np.sqrt(x[0]**2 + x[1]**2 + x[2]**2)
            
            # 改进的速度限制：使用平滑缩放而不是硬限幅
            if current_speed > max_speed:
                speed_scale = max_speed / current_speed
                x[0] *= speed_scale
                x[1] *= speed_scale
                x[2] *= speed_scale
            
            # 保持一个最小速度（改为平滑限制）
            min_speed = 0.3
            if current_speed < min_speed and x[0] < min_speed:
                # 平滑增加前进速度，而不是硬限幅
                speed_boost = (min_speed - current_speed) * 0.5
                if speed_boost > 0:
                    x[0] += speed_boost
            
            # 记录最终合速度
            final_speed = np.sqrt(x[0]**2 + x[1]**2 + x[2]**2)
            
            # 姿态角速率限制
            x[3] = LimitMaxMin(x[3], 0.2, -0.2)  # q 
            x[4] = LimitMaxMin(x[4], 0.2, -0.2)  # r
            
            # 姿态角限制
            x[8] = LimitMaxMin(x[8], 15 * D2R, -15 * D2R)  # theta 限制在±15度
            
            # 海流对航向的影响（减弱随机扰动，移除周期性扰动）
            # 1. 海流产生的横向力矩
            yaw_moment_current = np.sin(heading_current_diff) * current_strength * 0.15
            
            # 2. 添加随机湍流影响（减弱10倍）
            if i % 2 == 0:
                yaw_turbulence = 0.025 * np.random.randn() * (1.0 + 0.25 * j)  # 从0.25减为0.025
            else:
                yaw_turbulence = 0.01 * np.random.randn()  # 从0.1减为0.01
            
            # 3. 添加朝向目标的修正
            heading_correction_factor = 0.3  # 保持不变
            
            # 合并所有航向影响
            yaw_rate = yaw_moment_current + yaw_turbulence + heading_correction_factor * np.sin(target_heading_diff)
            x[9] = AdjustAngle(x[9] + yaw_rate)
            
            # 更新当前状态
            current_states[j] = x
            # 记录状态
            all_Y[j, i] = x
        
        # 计算当前编队中心位置
        formation_center_x[i] = current_center_x
        formation_center_y[i] = current_center_y
        
        # 计算当前编队形状
        for j in range(num_auvs):
            current_x_km = all_Y[j, i, 5] / 1000.0
            current_y_km = all_Y[j, i, 6] / 1000.0
            final_formation[j] = [current_x_km - current_center_x, current_y_km - current_center_y]
        
        # 检查是否达到目标区域中心（距离小于指定距离，更精确的条件）
        if dist_to_target <= target_reach_distance and not reached_target:
            reached_target = True
            target_reached_time = t
            print(f"\n编队中心在时间 {t:.1f} 秒到达目标区域中心! 距离: {dist_to_target:.2f}km")
            
            # 如果启用自动停止，则终止仿真
            if auto_stop:
                print(f"自动停止已启用，仿真将在达到目标后终止。")
                # 记录实际的仿真步数
                actual_steps = i + 1
                print(f"总仿真步数: {actual_steps}/{m} ({actual_steps/m*100:.2f}%)")
                break
            else:
                print("编队已到达目标区域中心，但仿真继续进行...")
        
        # 优化数据记录 - 只在特定步数记录数据，减少内存使用和计算量
        if i % data_record_interval == 0:
            # 保存数据点
            all_data['time'].append(t)
            all_data['formation_center_x'].append(current_center_x)
            all_data['formation_center_y'].append(current_center_y)
            all_data['dist_to_target'].append(dist_to_target)
            all_data['formation_deviation'].append(current_formation_deviation / num_auvs)  # 平均偏差
            
            # 减少能量数据记录频率
            if use_flexible_model:
                energy_data['time'].append(t)
                energy_data['kinetic_energy'].append(energy_metrics['kinetic_energy'])
                energy_data['potential_energy'].append(energy_metrics['potential_energy'])
                
                # 记录累积值
                energy_data['damping_energy'].append(cumulative_energy['damping_energy'])
                energy_data['thrust_work'].append(cumulative_energy['thrust_work'])
                energy_data['current_exchange'].append(cumulative_energy['current_exchange'])
                energy_data['total_energy'].append(
                    energy_metrics['kinetic_energy'] + 
                    energy_metrics['potential_energy'] + 
                    cumulative_energy['thrust_work'] + 
                    cumulative_energy['current_exchange'] - 
                    cumulative_energy['damping_energy']
                )
        
        # 减少进度输出频率以提高计算速度
        if i % progress_output_interval == 0:  # 使用计算得到的进度输出间隔
            elapsed = time.time() - start_time
            percent_done = i/m*100
            print(f"仿真进度: {i}/{m} 步 ({percent_done:.1f}%) - 已用时间: {elapsed:.1f}秒 - 速度: {i/max(elapsed,1):.1f}步/秒", end="\r")
            if i > 0 and i % detail_output_interval == 0:  # 使用计算得到的详细输出间隔
                print(f"\n编队中心位置: ({formation_center_x[i]:.1f}, {formation_center_y[i]:.1f}) km, 距目标: {dist_to_target:.1f} km")
    
    # 确保我们有一个有效的actual_steps变量
    if 'actual_steps' not in locals():
        actual_steps = m
    
    print(f"\n仿真结束! 总用时: {time.time() - start_time:.1f}秒, 实际步数: {actual_steps}/{m}")
    
    # 截取实际使用的数据
    T = T[:actual_steps]
    all_Y = all_Y[:, :actual_steps, :]
    all_current_effects = all_current_effects[:, :actual_steps, :]
    formation_center_x = formation_center_x[:actual_steps]
    formation_center_y = formation_center_y[:actual_steps]
    
    print("开始生成图表...")
    
    # 将AUV轨迹转换为相对坐标（千米）
    all_tracks_x_km = []
    all_tracks_y_km = []
    
    # 直接使用模型坐标，不进行经纬度转换
    for j in range(num_auvs):
        # 将米转换为千米
        track_x_km = all_Y[j, :, 5] / 1000.0  # x位置，转换为千米
        track_y_km = all_Y[j, :, 6] / 1000.0  # y位置，转换为千米
        
        all_tracks_x_km.append(track_x_km)
        all_tracks_y_km.append(track_y_km)
        
    # 将轨迹数据转换为DataFrame并保存
    trajectory_data = []
    for j in range(num_auvs):
        for i in range(len(T)):
            trajectory_data.append({
                'time': T[i],
                'auv_id': j+1,
                'x_km': all_tracks_x_km[j][i],
                'y_km': all_tracks_y_km[j][i],
                'u': all_Y[j, i, 0],
                'v': all_Y[j, i, 1],
                'w': all_Y[j, i, 2],
                'heading': all_Y[j, i, 9] * R2D,
                'current_u': all_current_effects[j, i, 0],
                'current_v': all_current_effects[j, i, 1],
                'current_strength': all_current_effects[j, i, 2]
            })
    
    # 创建并保存轨迹数据文件
    trajectory_df = pd.DataFrame(trajectory_data)
    trajectory_csv_path = os.path.join(output_dir, 'trajectory_data.csv')
    trajectory_df.to_csv(trajectory_csv_path, index=False)
    print(f"轨迹数据已保存到: {trajectory_csv_path}")
    
    # 创建并保存编队数据文件
    formation_df = pd.DataFrame(all_data)
    formation_csv_path = os.path.join(output_dir, 'formation_data.csv')
    formation_df.to_csv(formation_csv_path, index=False)
    print(f"编队数据已保存到: {formation_csv_path}")
    
    # 绘制AUV轨迹图 - 与circle.py格式一致
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # 使用实际海流数据绘制背景
    speed = np.clip(np.sqrt(uo_plot**2 + vo_plot**2), 0.06, 0.11)
    
    contour = ax.contourf(
        dx_km, dy_km, speed,
        cmap='RdBu_r',     # 颜色图选择
        levels=50,          # 等高线数
        vmin=0.06,          # 手动设置最小值
        vmax=0.11           # 手动设置最大值
    )
    
    # 添加颜色条 - 精确匹配circle.py的设置
    tick_min = 0.06
    tick_max = 0.11
    tick_num = 11  # 11个刻度
    cbar = fig.colorbar(contour, ax=ax, ticks=np.linspace(tick_min, tick_max, tick_num))
    cbar.set_label('Current Speed [m/s]', fontsize=16)
    cbar.ax.tick_params(labelsize=14)
    
    # 绘制海流箭头
    factor = 2
    uo_dense = zoom(uo_plot, factor)
    vo_dense = zoom(vo_plot, factor)
    x_dense = zoom(dx_km, factor)
    y_dense = zoom(dy_km, factor)
    
    # 画更密的箭头
    ax.quiver(x_dense, y_dense, uo_dense, vo_dense, scale=3e0, color='k', width=0.002)
    
    # 绘制目标区域
    target_circle = plt.Circle((target_x, target_y), target_radius, color='g', fill=False, linestyle='--', linewidth=2)
    ax.add_patch(target_circle)
    ax.plot(target_x, target_y, 'g*', markersize=15)  # 目标点标记
    ax.text(target_x + 2, target_y + 2, '目标区域', fontsize=12, color='g')
    
    # 绘制AUV初始圆形排列
    initial_x_km = []
    initial_y_km = []
    
    for j in range(num_auvs):
        # 使用第一个点作为初始位置
        initial_x_km.append(all_tracks_x_km[j][0])
        initial_y_km.append(all_tracks_y_km[j][0])
    
    # 绘制初始圆形排列
    initial_x_km_closed = np.append(initial_x_km, initial_x_km[0])
    initial_y_km_closed = np.append(initial_y_km, initial_y_km[0])
    
    ax.plot(initial_x_km_closed, initial_y_km_closed, color='red', linewidth=1.5, linestyle='-')  # 连线
    ax.plot(initial_x_km, initial_y_km, marker='^', color='red', markersize=8, linestyle='None')  # 三角形标记
    
    # 添加初始编队标签
    ax.plot([], [], color='red', linewidth=1.5, linestyle='-', label='Initial Formation')
    
    # 绘制AUV轨迹 - 使用较细的线条，不绘制航向箭头
    for j in range(num_auvs):
        # 绘制完整轨迹
        ax.plot(all_tracks_x_km[j], all_tracks_y_km[j], '-', color=colors[j], linewidth=1.0, label=f'AUV{j+1}')
        
        # 绘制起点和终点
        ax.plot(all_tracks_x_km[j][0], all_tracks_y_km[j][0], 'o', color=colors[j], markersize=6)
        ax.plot(all_tracks_x_km[j][-1], all_tracks_y_km[j][-1], 's', color=colors[j], markersize=6)
    
    # 绘制最终编队形状
    final_x_km = []
    final_y_km = []
    for j in range(num_auvs):
        final_x_km.append(all_tracks_x_km[j][-1])
        final_y_km.append(all_tracks_y_km[j][-1])
    
    # 闭合最终编队形状
    final_x_km_closed = np.append(final_x_km, final_x_km[0])
    final_y_km_closed = np.append(final_y_km, final_y_km[0])
    
    # 绘制最终编队形状（红色虚线）
    ax.plot(final_x_km_closed, final_y_km_closed, 'r--', linewidth=1.5, label='Final Formation')
    
    # 绘制编队中心轨迹
    ax.plot(formation_center_x, formation_center_y, 'k--', linewidth=1.5, label='Formation Center')
    
    # 标签 & 样式 - 精确匹配circle.py
    ax.set_xlabel("X [km]", fontsize=15)
    ax.set_ylabel("Y [km]", fontsize=15)
    ax.set_title("AUVs Moving to Target Area with Ocean Current", fontsize=20)
    
    # 精确设置坐标轴原点
    ax.spines['left'].set_position(('data', 0))
    ax.spines['bottom'].set_position(('data', 0))
    ax.spines['right'].set_color('none')
    ax.spines['top'].set_color('none')
    
    # 设置刻度位置
    ax.xaxis.set_ticks_position('bottom')
    ax.yaxis.set_ticks_position('left')
    ax.set_xticks(np.arange(0, dx_km.max(), 20))
    ax.set_yticks(np.arange(0, dy_km.max(), 20))
    
    ax.set_xlim(0, dx_km.max())
    ax.set_ylim(0, dy_km.max())
    ax.set_aspect('equal')
    ax.grid(False)
    
    # 添加图例，调整位置和大小 - 移到左上角
    ax.legend(loc='upper left', fontsize=10, framealpha=0.7)
    
    # 紧凑布局，去除白边
    fig.subplots_adjust(left=0.12, right=0.92, top=0.92, bottom=0.12)
    
    # 打印坐标范围
    print("X范围: 0 ~", dx_km.max(), "km")
    print("Y范围: 0 ~", dy_km.max(), "km")
    
    # 保存轨迹图
    trajectory_plot_path = os.path.join(output_dir, 'trajectory_plot.png')
    plt.savefig(trajectory_plot_path, dpi=300, bbox_inches='tight')
    print(f"轨迹图已保存到: {trajectory_plot_path}")
    
    # 如果到达目标，显示到达时间
    if reached_target:
        print(f"编队成功到达目标区域，用时 {target_reached_time:.1f} 秒")
    else:
        print("仿真结束，编队未能到达目标区域")
    
    # 计算编队形状保持指标
    initial_distances = []
    final_distances = []
    
    # 计算初始和最终相邻AUV之间的距离
    for j in range(num_auvs):
        next_j = (j + 1) % num_auvs
        initial_dist = np.sqrt((initial_x_km[j] - initial_x_km[next_j])**2 + 
                              (initial_y_km[j] - initial_y_km[next_j])**2)
        final_dist = np.sqrt((final_x_km[j] - final_x_km[next_j])**2 + 
                            (final_y_km[j] - final_y_km[next_j])**2)
        
        initial_distances.append(initial_dist)
        final_distances.append(final_dist)
    
    # 计算距离变化率
    distance_changes = [abs(final_distances[i] - initial_distances[i]) / initial_distances[i] * 100 
                        for i in range(num_auvs)]
    avg_distance_change = np.mean(distance_changes)
    
    print(f"\n编队形状保持分析:")
    print(f"初始平均相邻距离: {np.mean(initial_distances):.2f} km")
    print(f"最终平均相邻距离: {np.mean(final_distances):.2f} km")
    print(f"平均距离变化率: {avg_distance_change:.2f}%")
    
    if avg_distance_change < 20:
        print("结论: 编队形状保持良好 (变化率<20%)")
    elif avg_distance_change < 50:
        print("结论: 编队形状部分保持 (变化率<50%)")
    else:
        print("结论: 编队形状变化显著 (变化率>=50%)")
    
    # 如果需要保存当前仿真状态
    if save_results:
        # 保存到输出目录
        state_file_path = os.path.join(output_dir, 'simulation_state.pkl')
        save_simulation_state(state_file_path, 
                              actual_steps-1, 
                              T, 
                              all_Y, 
                              current_states, 
                              formation_center_x, 
                              formation_center_y, 
                              reached_target, 
                              target_reached_time, 
                              all_current_effects,
                              initial_formation,
                              final_formation,
                              energy_data if use_flexible_model else None)
        
        # 生成配置和结果摘要
        summary_data = {
            '仿真步数': actual_steps,
            '时间步长(秒)': h,
            'AUV数量': num_auvs,
            '目标坐标(km)': f"({target_x}, {target_y})",
            '目标区域半径(km)': target_radius,
            '自动停止': auto_stop,
            '目标达到距离(km)': target_reach_distance,
            '编队保持距离(km)': formation_keep_distance,
            '到达目标': reached_target,
            '到达时间(秒)': target_reached_time if reached_target else "未到达",
            '平均相邻距离变化率(%)': avg_distance_change,
            '编队形状保持评价': "良好" if avg_distance_change < 20 else "部分保持" if avg_distance_change < 50 else "变化显著"
        }
        
        # 添加柔性动力学相关信息
        if use_flexible_model:
            # 计算最终速度一致性指标
            final_speeds = []
            for j in range(num_auvs):
                u = all_Y[j, -1, 0]
                v = all_Y[j, -1, 1]
                speed = np.sqrt(u**2 + v**2)
                final_speeds.append(speed)
            
            final_speeds_array = np.array(final_speeds)
            final_speed_std = np.std(final_speeds_array)
            final_speed_mean = np.mean(final_speeds_array)
            final_speed_cv = final_speed_std / final_speed_mean if final_speed_mean > 0 else 0
            final_speed_range = np.max(final_speeds_array) - np.min(final_speeds_array)
            
            # 计算能量分布均衡性
            if energy_metrics.get('kinetic_energies') and len(energy_metrics['kinetic_energies']) == num_auvs:
                ke_std = np.std(energy_metrics['kinetic_energies'])
                ke_mean = np.mean(energy_metrics['kinetic_energies'])
                ke_cv = ke_std / ke_mean if ke_mean > 0 else 0
                
                energy_balance = "优" if ke_cv < 0.1 else "良" if ke_cv < 0.2 else "中" if ke_cv < 0.3 else "差"
            else:
                energy_balance = "未计算"
                
            # 计算海流扰动抵抗性能
            if 'current_powers' in energy_metrics and 'thrust_powers' in energy_metrics:
                # 推力与海流功率比值，作为抵抗海流能力指标
                current_resist_ratio = np.sum(np.abs(energy_metrics['thrust_powers'])) / (np.sum(np.abs(energy_metrics['current_powers'])) + 1e-6)
                current_resist_rating = "强" if current_resist_ratio > 2.0 else "中" if current_resist_ratio > 1.0 else "弱"
            else:
                current_resist_rating = "未计算"
            
            summary_data.update({
                '动力学模型': "柔性多体动力学",
                '编队拓扑': formation_topology,
                '弹簧刚度': spring_stiffness,
                '阻尼系数': damping_coefficient,
                '速度共识增益': consensus_gain,
                'AUV质量(kg)': auv_mass,
                '弹簧势能(最终)': energy_data['potential_energy'][-1] if energy_data['potential_energy'] else "NA",
                '动能(最终)': energy_data['kinetic_energy'][-1] if energy_data['kinetic_energy'] else "NA",
                '阻尼能耗(最终)': energy_data['damping_energy'][-1] if energy_data['damping_energy'] else "NA",
                '推力做功(最终)': energy_data['thrust_work'][-1] if energy_data['thrust_work'] else "NA",
                '海流能量交换(最终)': energy_data['current_exchange'][-1] if energy_data['current_exchange'] else "NA",
                '总能量(最终)': energy_data['total_energy'][-1] if energy_data['total_energy'] else "NA",
                '速度一致性(变异系数)': f"{final_speed_cv:.3f}",
                '速度范围(m/s)': f"{final_speed_range:.3f} ({np.min(final_speeds_array):.2f}-{np.max(final_speeds_array):.2f})",
                '能量分布均衡性': energy_balance,
                '海流扰动抵抗性能': current_resist_rating,
                '连接总数': formation_graph.number_of_edges() if formation_graph else 0
            })
        else:
            summary_data['动力学模型'] = "标准动力学"
        
        # 写入摘要文件
        summary_path = os.path.join(output_dir, 'simulation_summary.txt')
        with open(summary_path, 'w') as f:
            for key, value in summary_data.items():
                f.write(f"{key}: {value}\n")
        print(f"仿真摘要已保存到: {summary_path}")
    
    # 显示图像
    plt.show()
    
    # ==== 生成编队形状保持指标图 ====
    plt.figure(figsize=(12, 6))
    
    # 计算每个时间点的编队形状维持指标
    formation_maintenance = []
    formation_time_points = []
    # 使用计算得到的编队计算间隔
    for t_idx in range(0, len(T), formation_calc_interval):
        # 获取该时间点的相对位置
        current_rel_positions = []
        for j in range(num_auvs):
            x_km = all_Y[j, t_idx, 5] / 1000.0
            y_km = all_Y[j, t_idx, 6] / 1000.0
            rel_x = x_km - formation_center_x[t_idx]
            rel_y = y_km - formation_center_y[t_idx]
            current_rel_positions.append([rel_x, rel_y])
        
        # 计算与初始编队形状的总偏差
        total_deviation = 0
        for j in range(num_auvs):
            dev = np.sqrt((current_rel_positions[j][0] - initial_formation[j][0])**2 + 
                          (current_rel_positions[j][1] - initial_formation[j][1])**2)
            total_deviation += dev
        
        # 平均偏差
        formation_maintenance.append(total_deviation / num_auvs)
        formation_time_points.append(T[t_idx])
    
    # 绘制编队形状保持指标
    plt.figure(figsize=(12, 6))
    plt.plot(formation_time_points, formation_maintenance, 'b-', linewidth=2)
    plt.xlabel('时间 (秒)', fontsize=12)
    plt.ylabel('平均编队形状偏差 (km)', fontsize=12)
    plt.title('编队形状维持指标随时间变化', fontsize=14)
    plt.grid(True, alpha=0.3)
    
    # 保存编队形状维持指标图
    formation_metric_path = os.path.join(output_dir, 'formation_maintenance_metric.png')
    plt.savefig(formation_metric_path, dpi=300, bbox_inches='tight')
    print(f"编队形状维持指标图已保存到: {formation_metric_path}")
    
    plt.show()
    
    # ==== 生成能量分析图 ====
    if use_flexible_model:
        # 转换为DataFrame方便处理
        energy_df = pd.DataFrame(energy_data)
        
        # 绘制动能和势能
        plt.figure(figsize=(12, 6))
        plt.plot(energy_df['time'], energy_df['kinetic_energy'], 'b-', linewidth=2, label='动能')
        plt.plot(energy_df['time'], energy_df['potential_energy'], 'r-', linewidth=2, label='势能 (弹簧)')
        plt.xlabel('时间 (秒)', fontsize=12)
        plt.ylabel('能量 (J)', fontsize=12)
        plt.title('柔性多体动力学能量分析 - 动能与势能', fontsize=14)
        plt.grid(True, alpha=0.3)
        plt.legend(fontsize=12)
        
        # 保存能量分析图
        energy_plot_path = os.path.join(output_dir, 'energy_kinetic_potential.png')
        plt.savefig(energy_plot_path, dpi=300, bbox_inches='tight')
        print(f"动能与势能分析图已保存到: {energy_plot_path}")
        
        plt.show()
        
        # 绘制累积能量交换
        plt.figure(figsize=(12, 6))
        plt.plot(energy_df['time'], energy_df['thrust_work'], 'g-', linewidth=2, label='推力做功')
        plt.plot(energy_df['time'], energy_df['damping_energy'], 'r-', linewidth=2, label='阻尼能耗')
        plt.plot(energy_df['time'], energy_df['current_exchange'], 'b-', linewidth=2, label='海流能量交换')
        plt.xlabel('时间 (秒)', fontsize=12)
        plt.ylabel('累积能量 (J)', fontsize=12)
        plt.title('柔性多体动力学能量分析 - 累积能量交换', fontsize=14)
        plt.grid(True, alpha=0.3)
        plt.legend(fontsize=12)
        
        # 保存能量交换图
        energy_exchange_path = os.path.join(output_dir, 'energy_exchange.png')
        plt.savefig(energy_exchange_path, dpi=300, bbox_inches='tight')
        print(f"能量交换分析图已保存到: {energy_exchange_path}")
        
        plt.show()
        
        # 绘制总能量变化
        plt.figure(figsize=(12, 6))
        plt.plot(energy_df['time'], energy_df['total_energy'], 'k-', linewidth=2)
        plt.xlabel('时间 (秒)', fontsize=12)
        plt.ylabel('总能量 (J)', fontsize=12)
        plt.title('柔性多体动力学系统总能量变化', fontsize=14)
        plt.grid(True, alpha=0.3)
        
        # 保存总能量图
        total_energy_path = os.path.join(output_dir, 'total_energy.png')
        plt.savefig(total_energy_path, dpi=300, bbox_inches='tight')
        print(f"总能量分析图已保存到: {total_energy_path}")
        
        plt.show()
        
        # ===== 新增: 速度一致性分析图表 =====
        # 计算每个时间点的速度标准差和变异系数
        speed_stats = {'time': [], 'std': [], 'cv': [], 'max_diff': []}
        
        # 分析速度一致性，使用计算得到的速度一致性计算间隔
        for i in range(0, len(T), velocity_calc_interval):
            speeds = []
            for j in range(num_auvs):
                u = all_Y[j, i, 0]
                v = all_Y[j, i, 1]
                speed = np.sqrt(u**2 + v**2)
                speeds.append(speed)
            
            speeds_array = np.array(speeds)
            speed_std = np.std(speeds_array)
            speed_mean = np.mean(speeds_array)
            speed_cv = speed_std / speed_mean if speed_mean > 0 else 0
            max_diff = np.max(speeds_array) - np.min(speeds_array)
            
            speed_stats['time'].append(T[i])
            speed_stats['std'].append(speed_std)
            speed_stats['cv'].append(speed_cv)
            speed_stats['max_diff'].append(max_diff)
        
        # 绘制速度一致性变化图
        plt.figure(figsize=(12, 6))
        plt.plot(speed_stats['time'], speed_stats['cv'], 'b-', linewidth=2, label='速度变异系数')
        plt.plot(speed_stats['time'], speed_stats['std'], 'r-', linewidth=2, label='速度标准差 (m/s)')
        plt.xlabel('时间 (秒)', fontsize=12)
        plt.ylabel('速度一致性指标', fontsize=12)
        plt.title('编队速度一致性随时间变化', fontsize=14)
        plt.grid(True, alpha=0.3)
        plt.legend(fontsize=12)
        
        # 保存速度一致性图
        velocity_consistency_path = os.path.join(output_dir, 'velocity_consistency.png')
        plt.savefig(velocity_consistency_path, dpi=300, bbox_inches='tight')
        print(f"速度一致性分析图已保存到: {velocity_consistency_path}")
        
        plt.show()
        
        # ===== 新增: 能量分布分析 =====
        # 获取最后一个时间步的能量分布
        if energy_metrics.get('kinetic_energies') and len(energy_metrics['kinetic_energies']) == num_auvs:
            plt.figure(figsize=(10, 6))
            
            # 获取每个AUV的能量分布
            ke_dist = energy_metrics['kinetic_energies']
            thrust_dist = energy_metrics['thrust_powers']
            current_dist = energy_metrics['current_powers']
            
            # 生成柱状图数据
            auv_ids = [f'AUV{j+1}' for j in range(num_auvs)]
            x = np.arange(len(auv_ids))
            width = 0.25
            
            # 绘制柱状图
            fig, ax = plt.subplots(figsize=(14, 7))
            rects1 = ax.bar(x - width, ke_dist, width, label='动能')
            rects2 = ax.bar(x, thrust_dist, width, label='推力功率')
            rects3 = ax.bar(x + width, current_dist, width, label='海流功率')
            
            # 添加标签和图例
            ax.set_xlabel('AUV编号', fontsize=12)
            ax.set_ylabel('能量/功率 (J/W)', fontsize=12)
            ax.set_title('多AUV系统能量分布', fontsize=14)
            ax.set_xticks(x)
            ax.set_xticklabels(auv_ids)
            ax.legend()
            
            # 保存能量分布图
            energy_dist_path = os.path.join(output_dir, 'energy_distribution.png')
            plt.savefig(energy_dist_path, dpi=300, bbox_inches='tight')
            print(f"能量分布分析图已保存到: {energy_dist_path}")
            
            plt.show()
        
        # 保存能量数据
        energy_csv_path = os.path.join(output_dir, 'energy_analysis_data.csv')
        energy_df.to_csv(energy_csv_path, index=False)
        print(f"能量分析数据已保存到: {energy_csv_path}")
        
        # 保存速度一致性数据
        speed_stats_df = pd.DataFrame(speed_stats)
        speed_stats_csv_path = os.path.join(output_dir, 'velocity_consistency_data.csv')
        speed_stats_df.to_csv(speed_stats_csv_path, index=False)
        print(f"速度一致性数据已保存到: {speed_stats_csv_path}")
    
    # ==== 生成速度变化图 ====
    plt.figure(figsize=(12, 6))
    
    # 计算每个AUV的合速度
    for j in range(num_auvs):
        speeds = np.sqrt(all_Y[j, :, 0]**2 + all_Y[j, :, 1]**2)  # 合速度
        plt.plot(T, speeds, '-', color=colors[j], linewidth=1.0, label=f'AUV{j+1}')
    
    # 设置图表属性
    plt.xlabel('时间 (秒)', fontsize=12)
    plt.ylabel('速度 (m/s)', fontsize=12)
    plt.title('AUV速度随时间变化 - 受海流影响', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(loc='upper right', fontsize=10)
    
    # 显示图表
    plt.tight_layout()
    
    # 保存速度变化图
    speed_plot_path = os.path.join(output_dir, 'speed_plot.png')
    plt.savefig(speed_plot_path, dpi=300, bbox_inches='tight')
    print(f"速度变化图已保存到: {speed_plot_path}")
    
    plt.show()
    
    # ==== 生成航向角变化图 ====
    plt.figure(figsize=(12, 6))
    
    # 绘制每个AUV的航向角变化
    for j in range(num_auvs):
        headings = all_Y[j, :, 9] * R2D  # 转换为度
        plt.plot(T, headings, '-', color=colors[j], linewidth=1.0, label=f'AUV{j+1}')
    
    # 设置图表属性
    plt.xlabel('时间 (秒)', fontsize=12)
    plt.ylabel('航向角 (度)', fontsize=12)
    plt.title('AUV航向角随时间变化 - 受海流影响', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(loc='upper right', fontsize=10)
    
    # 显示图表
    plt.tight_layout()
    
    # 保存航向角变化图
    heading_plot_path = os.path.join(output_dir, 'heading_plot.png')
    plt.savefig(heading_plot_path, dpi=300, bbox_inches='tight')
    print(f"航向角变化图已保存到: {heading_plot_path}")
    
    plt.show()
    
    # ==== 生成编队中心到目标的距离变化图 ====
    plt.figure(figsize=(12, 6))
    
    # 计算编队中心到目标的距离
    distances_to_target = np.sqrt((formation_center_x - target_x)**2 + (formation_center_y - target_y)**2)
    
    # 绘制距离变化曲线
    plt.plot(T, distances_to_target, 'b-', linewidth=2.0)
    
    # 添加目标区域半径线
    plt.axhline(y=target_radius, color='g', linestyle='--', label='目标区域边界')
    
    # 如果到达目标，标记到达时刻
    if reached_target:
        plt.axvline(x=target_reached_time, color='r', linestyle='--', label=f'到达目标 ({target_reached_time:.1f}秒)')
    
    # 设置图表属性
    plt.xlabel('时间 (秒)', fontsize=12)
    plt.ylabel('到目标的距离 (km)', fontsize=12)
    plt.title('编队中心到目标距离变化', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=10)
    
    # 显示图表
    plt.tight_layout()
    
    # 保存距离变化图
    distance_plot_path = os.path.join(output_dir, 'distance_to_target_plot.png')
    plt.savefig(distance_plot_path, dpi=300, bbox_inches='tight')
    print(f"距离变化图已保存到: {distance_plot_path}")
    
    plt.show()
    
    # 验证并打印不同AUV的平均速度
    print("\n===== AUV速度与海流影响验证 =====")
    print("AUV编号 | 平均速度(m/s) | 平均海流强度(m/s) | 平均航向(度)")
    print("-" * 60)
    
    for j in range(num_auvs):
        # 计算平均速度
        avg_speed = np.mean(np.sqrt(all_Y[j, :, 0]**2 + all_Y[j, :, 1]**2))
        
        # 计算平均海流强度
        avg_current = np.mean(all_current_effects[j, :, 2])
        
        # 计算平均航向角
        avg_heading = np.mean(all_Y[j, :, 9]) * R2D
        
        # 打印结果
        print(f"AUV{j+1}    | {avg_speed:.2f}        | {avg_current:.2f}           | {avg_heading:.1f}")
    
    # 如果使用了柔性多体动力学模型，绘制编队拓扑结构
    if use_flexible_model and formation_graph:
        plt.figure(figsize=(8, 8))
        
        # 获取最终编队位置（最后一个时间步）
        final_positions = []
        for j in range(num_auvs):
            final_x = all_Y[j, -1, 5] / 1000.0  # 转换为千米
            final_y = all_Y[j, -1, 6] / 1000.0
            final_positions.append((final_x, final_y))
        
        # 计算节点位置
        node_pos = {j: final_positions[j] for j in range(num_auvs)}
        
        # 计算连接边的弹簧势能
        edge_widths = []
        for i, j in formation_graph.edges():
            # 计算相对位置向量
            ri = final_positions[i]
            rj = final_positions[j]
            r_ij = np.array(ri) - np.array(rj)
            r_norm = np.linalg.norm(r_ij)
            
            # 计算期望距离
            initial_pos_i = initial_formation[i]
            initial_pos_j = initial_formation[j]
            desired_dist = np.linalg.norm(initial_pos_i - initial_pos_j)
            
            # 弹簧势能 (用于边的宽度)
            spring_energy = 0.5 * spring_stiffness * (r_norm - desired_dist)**2
            edge_widths.append(0.5 + spring_energy / 1000.0)  # 归一化宽度
        
        # 绘制网络图
        nx.draw_networkx(formation_graph, pos=node_pos, 
                         node_color=colors[:num_auvs], 
                         node_size=500, 
                         width=edge_widths,
                         with_labels=True, 
                         font_weight='bold',
                         font_color='white')
        
        plt.title(f'编队拓扑结构 ({formation_topology})', fontsize=14)
        plt.axis('off')
        
        # 保存拓扑结构图
        topology_path = os.path.join(output_dir, 'formation_topology.png')
        plt.savefig(topology_path, dpi=300, bbox_inches='tight')
        print(f"编队拓扑结构图已保存到: {topology_path}")
        
        plt.show()
    
    # 在绘制完所有现有图表之后，添加三维可视化
    # 在show_plots结束后添加此代码
    if enable_3d_visualization and save_results:
        visualize_3d_ocean_flow(all_Y, all_current_effects, T, uo, vo, lat, lon, depth, output_dir)

# 为了保持向后兼容性，创建main函数的别名
def main(max_steps=80000, load_state=False, save_results=True, auto_stop=True, target_reach_distance=1.0,
         use_flexible_model=True, spring_stiffness=100.0, damping_coefficient=80.0,
         formation_topology='hierarchical', auv_mass=500.0, consensus_gain=0.2,
         data_record_interval=10, enable_3d_visualization=True):
    """原始main函数的别名，调用新的多阶段系统"""
    return main_multi_phase(max_steps, load_state, save_results, auto_stop, target_reach_distance,
                           use_flexible_model, spring_stiffness, damping_coefficient, formation_topology,
                           auv_mass, consensus_gain, data_record_interval, enable_3d_visualization)

if __name__ == "__main__":
    import argparse

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='多AUV编队仿真程序')
    parser.add_argument('--steps', type=int, default=800000, help='仿真步数 (默认: 800000)')
    parser.add_argument('--continue', dest='continue_sim', action='store_true', help='从上次保存的状态继续仿真')
    parser.add_argument('--no-save', dest='save_results', action='store_false', help='不保存仿真结果')
    parser.add_argument('--no-auto-stop', dest='auto_stop', action='store_false', help='关闭自动停止功能')
    parser.add_argument('--target-distance', type=float, default=1.0, help='目标达到判定距离(km) (默认: 1.0)')
    
    # 柔性动力学相关参数
    parser.add_argument('--no-flexible', dest='use_flexible', action='store_false', help='不使用柔性多体动力学模型')
    parser.add_argument('--stiffness', type=float, default=100.0, help='弹簧刚度系数 (默认: 100.0)')
    parser.add_argument('--damping', type=float, default=80.0, help='阻尼系数 (默认: 80.0)')
    parser.add_argument('--topology', type=str, 
                      choices=['complete', 'ring', 'star', 'mesh', 'hierarchical', 'adaptive'], 
                      default='hierarchical', help='编队拓扑结构 (默认: hierarchical)')
    parser.add_argument('--mass', type=float, default=500.0, help='AUV质量(kg) (默认: 500.0)')
    parser.add_argument('--consensus', type=float, default=0.2, help='速度共识增益 (默认: 0.2)')
    
    # 计算性能相关参数
    parser.add_argument('--record-interval', type=int, default=10, help='数据记录间隔步数 (默认: 10, 越大越快)')
    
    # 添加3D可视化参数
    parser.add_argument('--no-3d-vis', dest='enable_3d_visualization', action='store_false', 
                      help='不生成三维可视化 (默认启用)')
    
    parser.set_defaults(continue_sim=False, save_results=True, auto_stop=True, use_flexible=True, 
                       enable_3d_visualization=True)
    
    # 解析命令行参数
    args = parser.parse_args()
    
    try:
        print("=" * 60)
        print("多AUV编队仿真启动")
        print(f"步数: {args.steps}, 自动停止: {'启用' if args.auto_stop else '禁用'}, 柔性模型: {'启用' if args.use_flexible else '禁用'}")
        print(f"数据记录间隔: 每{args.record_interval}步, 目标判定距离: {args.target_distance}km")
        print(f"三维可视化: {'启用' if args.enable_3d_visualization else '禁用'}")
        print("=" * 60)
        
        # 使用解析的参数运行主函数
        main(max_steps=args.steps, 
             load_state=args.continue_sim, 
             save_results=args.save_results,
             auto_stop=args.auto_stop,
             target_reach_distance=args.target_distance,
             use_flexible_model=args.use_flexible,
             spring_stiffness=args.stiffness,
             damping_coefficient=args.damping,
             formation_topology=args.topology,
             auv_mass=args.mass,
             consensus_gain=args.consensus,
             data_record_interval=args.record_interval,
             enable_3d_visualization=args.enable_3d_visualization)  # 传递3D可视化参数
    except Exception as e:
        print("程序发生异常：", e)
        import traceback
        traceback.print_exc() 